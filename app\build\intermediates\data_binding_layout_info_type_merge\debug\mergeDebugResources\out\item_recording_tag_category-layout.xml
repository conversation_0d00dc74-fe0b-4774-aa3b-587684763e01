<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recording_tag_category" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\item_recording_tag_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_recording_tag_category_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="30" endOffset="14"/></Target><Target id="@+id/tv_category_name" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="25"/></Target><Target id="@+id/flexbox_tags" view="com.google.android.flexbox.FlexboxLayout"><Expressions/><location startLine="22" startOffset="4" endLine="28" endOffset="41"/></Target></Targets></Layout>