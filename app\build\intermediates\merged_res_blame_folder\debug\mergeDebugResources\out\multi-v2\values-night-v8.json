{"logs": [{"outputFile": "com.example.likeqianwang.app-mergeDebugResources-48:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0be8fe7a00cc17258e0156ab1fb27859\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,47", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,4149", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,4233"}}, {"source": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "13", "endColumns": "12", "endOffsets": "761"}, "to": {"startLines": "9", "startColumns": "4", "startOffsets": "687", "endLines": "20", "endColumns": "12", "endOffsets": "1256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\36a84f081851bde3ce49fbf8d9f79604\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1261,1336,1447,1536,1637,1744,1851,1950,2057,2160,2287,2375,2499,2601,2703,2819,2921,3035,3163,3279,3401,3537,3657,3791,3911,4023,4238,4355,4479,4609,4731,4869,5003,5119", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1331,1442,1531,1632,1739,1846,1945,2052,2155,2282,2370,2494,2596,2698,2814,2916,3030,3158,3274,3396,3532,3652,3786,3906,4018,4144,4350,4474,4604,4726,4864,4998,5114,5234"}}]}]}