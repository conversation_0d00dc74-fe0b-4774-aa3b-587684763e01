// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class WidgetInAndOutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView widgetInOut;

  @NonNull
  public final TextView widgetInOut1;

  @NonNull
  public final TextView widgetInOut2;

  @NonNull
  public final TextView widgetInOutBalanceAmount;

  @NonNull
  public final TextView widgetInOutTotalExpenseAmount;

  @NonNull
  public final TextView widgetInOutTotalIncomeAmount;

  private WidgetInAndOutBinding(@NonNull LinearLayout rootView, @NonNull TextView widgetInOut,
      @NonNull TextView widgetInOut1, @NonNull TextView widgetInOut2,
      @NonNull TextView widgetInOutBalanceAmount, @NonNull TextView widgetInOutTotalExpenseAmount,
      @NonNull TextView widgetInOutTotalIncomeAmount) {
    this.rootView = rootView;
    this.widgetInOut = widgetInOut;
    this.widgetInOut1 = widgetInOut1;
    this.widgetInOut2 = widgetInOut2;
    this.widgetInOutBalanceAmount = widgetInOutBalanceAmount;
    this.widgetInOutTotalExpenseAmount = widgetInOutTotalExpenseAmount;
    this.widgetInOutTotalIncomeAmount = widgetInOutTotalIncomeAmount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static WidgetInAndOutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static WidgetInAndOutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.widget_in_and_out, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static WidgetInAndOutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.widget_in_out_总支出;
      TextView widgetInOut = ViewBindings.findChildViewById(rootView, id);
      if (widgetInOut == null) {
        break missingId;
      }

      id = R.id.widget_in_out_总收入;
      TextView widgetInOut1 = ViewBindings.findChildViewById(rootView, id);
      if (widgetInOut1 == null) {
        break missingId;
      }

      id = R.id.widget_in_out_结余;
      TextView widgetInOut2 = ViewBindings.findChildViewById(rootView, id);
      if (widgetInOut2 == null) {
        break missingId;
      }

      id = R.id.widget_in_out_balance_amount;
      TextView widgetInOutBalanceAmount = ViewBindings.findChildViewById(rootView, id);
      if (widgetInOutBalanceAmount == null) {
        break missingId;
      }

      id = R.id.widget_in_out_total_expense_amount;
      TextView widgetInOutTotalExpenseAmount = ViewBindings.findChildViewById(rootView, id);
      if (widgetInOutTotalExpenseAmount == null) {
        break missingId;
      }

      id = R.id.widget_in_out_total_income_amount;
      TextView widgetInOutTotalIncomeAmount = ViewBindings.findChildViewById(rootView, id);
      if (widgetInOutTotalIncomeAmount == null) {
        break missingId;
      }

      return new WidgetInAndOutBinding((LinearLayout) rootView, widgetInOut, widgetInOut1,
          widgetInOut2, widgetInOutBalanceAmount, widgetInOutTotalExpenseAmount,
          widgetInOutTotalIncomeAmount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
