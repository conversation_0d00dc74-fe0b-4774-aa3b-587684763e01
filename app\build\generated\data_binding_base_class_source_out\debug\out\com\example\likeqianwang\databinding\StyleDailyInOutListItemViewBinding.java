// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleDailyInOutListItemViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView receiptDailyInOutDate;

  @NonNull
  public final RecyclerView receiptDailyInOutDetail;

  @NonNull
  public final TextView receiptDailyInOutStats;

  private StyleDailyInOutListItemViewBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView receiptDailyInOutDate, @NonNull RecyclerView receiptDailyInOutDetail,
      @NonNull TextView receiptDailyInOutStats) {
    this.rootView = rootView;
    this.receiptDailyInOutDate = receiptDailyInOutDate;
    this.receiptDailyInOutDetail = receiptDailyInOutDetail;
    this.receiptDailyInOutStats = receiptDailyInOutStats;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleDailyInOutListItemViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleDailyInOutListItemViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_daily_in_out_list_item_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleDailyInOutListItemViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.receipt_Daily_InOut_date;
      TextView receiptDailyInOutDate = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutDate == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_detail;
      RecyclerView receiptDailyInOutDetail = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutDetail == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_stats;
      TextView receiptDailyInOutStats = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutStats == null) {
        break missingId;
      }

      return new StyleDailyInOutListItemViewBinding((ConstraintLayout) rootView,
          receiptDailyInOutDate, receiptDailyInOutDetail, receiptDailyInOutStats);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
