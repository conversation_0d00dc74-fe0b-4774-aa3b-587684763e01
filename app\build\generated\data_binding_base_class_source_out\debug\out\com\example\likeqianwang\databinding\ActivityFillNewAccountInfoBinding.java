// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFillNewAccountInfoBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final EditText etWalletsAccountInputBalance;

  @NonNull
  public final EditText etWalletsAccountInputCurrentCredit;

  @NonNull
  public final TextView etWalletsAccountInputDueDate;

  @NonNull
  public final EditText etWalletsAccountInputName;

  @NonNull
  public final EditText etWalletsAccountInputRemark;

  @NonNull
  public final TextView etWalletsAccountInputStatementDate;

  @NonNull
  public final EditText etWalletsAccountInputTotalCredit;

  @NonNull
  public final ShapeableImageView ivWalletsAccountIcon;

  @NonNull
  public final SwitchMaterial switchWalletsAccountDueDateInCurrentPeriod;

  @NonNull
  public final SwitchMaterial switchWalletsAccountIncludeInAsset;

  @NonNull
  public final TextView tvWalletsAccountCurrency;

  @NonNull
  public final TextView tvWalletsAccountName;

  @NonNull
  public final TextView tvWalletsBack;

  @NonNull
  public final TextView tvWalletsFillInfoTitle;

  @NonNull
  public final TextView tvWalletsSave;

  @NonNull
  public final LinearLayout walletsAccountBasicInfo;

  @NonNull
  public final LinearLayout walletsAccountCreditInfo;

  @NonNull
  public final LinearLayout walletsAccountCurrencySelection;

  @NonNull
  public final LinearLayout walletsAccountInputBalanceArea;

  @NonNull
  public final LinearLayout walletsAccountType;

  @NonNull
  public final ConstraintLayout walletsFillNewAccountInfo;

  private ActivityFillNewAccountInfoBinding(@NonNull ConstraintLayout rootView,
      @NonNull EditText etWalletsAccountInputBalance,
      @NonNull EditText etWalletsAccountInputCurrentCredit,
      @NonNull TextView etWalletsAccountInputDueDate, @NonNull EditText etWalletsAccountInputName,
      @NonNull EditText etWalletsAccountInputRemark,
      @NonNull TextView etWalletsAccountInputStatementDate,
      @NonNull EditText etWalletsAccountInputTotalCredit,
      @NonNull ShapeableImageView ivWalletsAccountIcon,
      @NonNull SwitchMaterial switchWalletsAccountDueDateInCurrentPeriod,
      @NonNull SwitchMaterial switchWalletsAccountIncludeInAsset,
      @NonNull TextView tvWalletsAccountCurrency, @NonNull TextView tvWalletsAccountName,
      @NonNull TextView tvWalletsBack, @NonNull TextView tvWalletsFillInfoTitle,
      @NonNull TextView tvWalletsSave, @NonNull LinearLayout walletsAccountBasicInfo,
      @NonNull LinearLayout walletsAccountCreditInfo,
      @NonNull LinearLayout walletsAccountCurrencySelection,
      @NonNull LinearLayout walletsAccountInputBalanceArea,
      @NonNull LinearLayout walletsAccountType,
      @NonNull ConstraintLayout walletsFillNewAccountInfo) {
    this.rootView = rootView;
    this.etWalletsAccountInputBalance = etWalletsAccountInputBalance;
    this.etWalletsAccountInputCurrentCredit = etWalletsAccountInputCurrentCredit;
    this.etWalletsAccountInputDueDate = etWalletsAccountInputDueDate;
    this.etWalletsAccountInputName = etWalletsAccountInputName;
    this.etWalletsAccountInputRemark = etWalletsAccountInputRemark;
    this.etWalletsAccountInputStatementDate = etWalletsAccountInputStatementDate;
    this.etWalletsAccountInputTotalCredit = etWalletsAccountInputTotalCredit;
    this.ivWalletsAccountIcon = ivWalletsAccountIcon;
    this.switchWalletsAccountDueDateInCurrentPeriod = switchWalletsAccountDueDateInCurrentPeriod;
    this.switchWalletsAccountIncludeInAsset = switchWalletsAccountIncludeInAsset;
    this.tvWalletsAccountCurrency = tvWalletsAccountCurrency;
    this.tvWalletsAccountName = tvWalletsAccountName;
    this.tvWalletsBack = tvWalletsBack;
    this.tvWalletsFillInfoTitle = tvWalletsFillInfoTitle;
    this.tvWalletsSave = tvWalletsSave;
    this.walletsAccountBasicInfo = walletsAccountBasicInfo;
    this.walletsAccountCreditInfo = walletsAccountCreditInfo;
    this.walletsAccountCurrencySelection = walletsAccountCurrencySelection;
    this.walletsAccountInputBalanceArea = walletsAccountInputBalanceArea;
    this.walletsAccountType = walletsAccountType;
    this.walletsFillNewAccountInfo = walletsFillNewAccountInfo;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFillNewAccountInfoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFillNewAccountInfoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_fill_new_account_info, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFillNewAccountInfoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_wallets_account_input_balance;
      EditText etWalletsAccountInputBalance = ViewBindings.findChildViewById(rootView, id);
      if (etWalletsAccountInputBalance == null) {
        break missingId;
      }

      id = R.id.et_wallets_account_input_current_credit;
      EditText etWalletsAccountInputCurrentCredit = ViewBindings.findChildViewById(rootView, id);
      if (etWalletsAccountInputCurrentCredit == null) {
        break missingId;
      }

      id = R.id.et_wallets_account_input_due_date;
      TextView etWalletsAccountInputDueDate = ViewBindings.findChildViewById(rootView, id);
      if (etWalletsAccountInputDueDate == null) {
        break missingId;
      }

      id = R.id.et_wallets_account_input_name;
      EditText etWalletsAccountInputName = ViewBindings.findChildViewById(rootView, id);
      if (etWalletsAccountInputName == null) {
        break missingId;
      }

      id = R.id.et_wallets_account_input_remark;
      EditText etWalletsAccountInputRemark = ViewBindings.findChildViewById(rootView, id);
      if (etWalletsAccountInputRemark == null) {
        break missingId;
      }

      id = R.id.et_wallets_account_input_statement_date;
      TextView etWalletsAccountInputStatementDate = ViewBindings.findChildViewById(rootView, id);
      if (etWalletsAccountInputStatementDate == null) {
        break missingId;
      }

      id = R.id.et_wallets_account_input_total_credit;
      EditText etWalletsAccountInputTotalCredit = ViewBindings.findChildViewById(rootView, id);
      if (etWalletsAccountInputTotalCredit == null) {
        break missingId;
      }

      id = R.id.iv_wallets_account_icon;
      ShapeableImageView ivWalletsAccountIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivWalletsAccountIcon == null) {
        break missingId;
      }

      id = R.id.switch_wallets_account_due_date_in_current_period;
      SwitchMaterial switchWalletsAccountDueDateInCurrentPeriod = ViewBindings.findChildViewById(rootView, id);
      if (switchWalletsAccountDueDateInCurrentPeriod == null) {
        break missingId;
      }

      id = R.id.switch_wallets_account_includeInAsset;
      SwitchMaterial switchWalletsAccountIncludeInAsset = ViewBindings.findChildViewById(rootView, id);
      if (switchWalletsAccountIncludeInAsset == null) {
        break missingId;
      }

      id = R.id.tv_wallets_account_currency;
      TextView tvWalletsAccountCurrency = ViewBindings.findChildViewById(rootView, id);
      if (tvWalletsAccountCurrency == null) {
        break missingId;
      }

      id = R.id.tv_wallets_account_name;
      TextView tvWalletsAccountName = ViewBindings.findChildViewById(rootView, id);
      if (tvWalletsAccountName == null) {
        break missingId;
      }

      id = R.id.tv_wallets_back;
      TextView tvWalletsBack = ViewBindings.findChildViewById(rootView, id);
      if (tvWalletsBack == null) {
        break missingId;
      }

      id = R.id.tv_wallets_fill_info_title;
      TextView tvWalletsFillInfoTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvWalletsFillInfoTitle == null) {
        break missingId;
      }

      id = R.id.tv_wallets_save;
      TextView tvWalletsSave = ViewBindings.findChildViewById(rootView, id);
      if (tvWalletsSave == null) {
        break missingId;
      }

      id = R.id.wallets_account_basic_info;
      LinearLayout walletsAccountBasicInfo = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountBasicInfo == null) {
        break missingId;
      }

      id = R.id.wallets_account_credit_info;
      LinearLayout walletsAccountCreditInfo = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountCreditInfo == null) {
        break missingId;
      }

      id = R.id.wallets_account_currency_selection;
      LinearLayout walletsAccountCurrencySelection = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountCurrencySelection == null) {
        break missingId;
      }

      id = R.id.wallets_account_input_balance_area;
      LinearLayout walletsAccountInputBalanceArea = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountInputBalanceArea == null) {
        break missingId;
      }

      id = R.id.wallets_account_type;
      LinearLayout walletsAccountType = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountType == null) {
        break missingId;
      }

      ConstraintLayout walletsFillNewAccountInfo = (ConstraintLayout) rootView;

      return new ActivityFillNewAccountInfoBinding((ConstraintLayout) rootView,
          etWalletsAccountInputBalance, etWalletsAccountInputCurrentCredit,
          etWalletsAccountInputDueDate, etWalletsAccountInputName, etWalletsAccountInputRemark,
          etWalletsAccountInputStatementDate, etWalletsAccountInputTotalCredit,
          ivWalletsAccountIcon, switchWalletsAccountDueDateInCurrentPeriod,
          switchWalletsAccountIncludeInAsset, tvWalletsAccountCurrency, tvWalletsAccountName,
          tvWalletsBack, tvWalletsFillInfoTitle, tvWalletsSave, walletsAccountBasicInfo,
          walletsAccountCreditInfo, walletsAccountCurrencySelection, walletsAccountInputBalanceArea,
          walletsAccountType, walletsFillNewAccountInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
