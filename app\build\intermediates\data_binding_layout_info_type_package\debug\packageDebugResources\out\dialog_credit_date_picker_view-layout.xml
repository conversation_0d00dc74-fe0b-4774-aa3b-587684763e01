<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_credit_date_picker_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_credit_date_picker_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_credit_date_picker_view_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="88" endOffset="51"/></Target><Target id="@+id/drag_handle" view="View"><Expressions/><location startLine="12" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/tv_CreditDatePicker_confirm" view="TextView"><Expressions/><location startLine="22" startOffset="4" endLine="34" endOffset="55"/></Target><Target id="@+id/tv_CreditDatePicker_cancel" view="TextView"><Expressions/><location startLine="36" startOffset="4" endLine="48" endOffset="76"/></Target><Target id="@+id/number_picker_day" view="NumberPicker"><Expressions/><location startLine="68" startOffset="8" endLine="74" endOffset="51"/></Target></Targets></Layout>