<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="style_daily_in_out_list_item_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\style_daily_in_out_list_item_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/style_daily_in_out_list_item_view_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="39" endOffset="51"/></Target><Target id="@+id/receipt_Daily_InOut_date" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="14" endOffset="32"/></Target><Target id="@+id/receipt_Daily_InOut_stats" view="TextView"><Expressions/><location startLine="16" startOffset="4" endLine="23" endOffset="51"/></Target><Target id="@+id/receipt_Daily_InOut_detail" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="25" startOffset="4" endLine="37" endOffset="76"/></Target></Targets></Layout>