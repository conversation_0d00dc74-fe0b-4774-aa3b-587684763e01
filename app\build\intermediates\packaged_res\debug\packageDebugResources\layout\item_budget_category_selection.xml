<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:padding="12dp">

    <ImageView
        android:id="@+id/category_selection_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:contentDescription="@string/budget_desc_分类图标"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_category_food" />

    <TextView
        android:id="@+id/category_selection_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:textColor="@color/black"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/category_selection_checkbox"
        app:layout_constraintStart_toEndOf="@id/category_selection_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="餐饮" />

    <CheckBox
        android:id="@+id/category_selection_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
