// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogRecordingTransferAccountSelectionViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final TextView recordingPageTransferAccountBack;

  @NonNull
  public final RecyclerView recordingPageTransferAccountList;

  @NonNull
  public final TextView recordingPageTransferAccountTitle;

  private DialogRecordingTransferAccountSelectionViewBinding(@NonNull LinearLayout rootView,
      @NonNull View dragHandle, @NonNull TextView recordingPageTransferAccountBack,
      @NonNull RecyclerView recordingPageTransferAccountList,
      @NonNull TextView recordingPageTransferAccountTitle) {
    this.rootView = rootView;
    this.dragHandle = dragHandle;
    this.recordingPageTransferAccountBack = recordingPageTransferAccountBack;
    this.recordingPageTransferAccountList = recordingPageTransferAccountList;
    this.recordingPageTransferAccountTitle = recordingPageTransferAccountTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogRecordingTransferAccountSelectionViewBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogRecordingTransferAccountSelectionViewBinding inflate(
      @NonNull LayoutInflater inflater, @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_recording_transfer_account_selection_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogRecordingTransferAccountSelectionViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.recording_page_transfer_account_back;
      TextView recordingPageTransferAccountBack = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageTransferAccountBack == null) {
        break missingId;
      }

      id = R.id.recording_page_transfer_account_list;
      RecyclerView recordingPageTransferAccountList = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageTransferAccountList == null) {
        break missingId;
      }

      id = R.id.recording_page_transfer_account_title;
      TextView recordingPageTransferAccountTitle = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageTransferAccountTitle == null) {
        break missingId;
      }

      return new DialogRecordingTransferAccountSelectionViewBinding((LinearLayout) rootView,
          dragHandle, recordingPageTransferAccountBack, recordingPageTransferAccountList,
          recordingPageTransferAccountTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
