// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.example.likeqianwang.R;
import com.example.likeqianwang.Utils.NestedScrollableHost;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentReceiptBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton buttonAdd;

  @NonNull
  public final RecyclerView receiptDailyInOutList;

  @NonNull
  public final NestedScrollableHost receiptInOutBudgetContainer;

  @NonNull
  public final ViewPager2 receiptInOutBudgetWidget;

  @NonNull
  public final TabLayout receiptInOutBudgetWidgetTabs;

  @NonNull
  public final TextView receiptReceipts;

  @NonNull
  public final ImageView receiptSetting;

  @NonNull
  public final TextView receiptYearMonth;

  private FragmentReceiptBinding(@NonNull ConstraintLayout rootView, @NonNull ImageButton buttonAdd,
      @NonNull RecyclerView receiptDailyInOutList,
      @NonNull NestedScrollableHost receiptInOutBudgetContainer,
      @NonNull ViewPager2 receiptInOutBudgetWidget, @NonNull TabLayout receiptInOutBudgetWidgetTabs,
      @NonNull TextView receiptReceipts, @NonNull ImageView receiptSetting,
      @NonNull TextView receiptYearMonth) {
    this.rootView = rootView;
    this.buttonAdd = buttonAdd;
    this.receiptDailyInOutList = receiptDailyInOutList;
    this.receiptInOutBudgetContainer = receiptInOutBudgetContainer;
    this.receiptInOutBudgetWidget = receiptInOutBudgetWidget;
    this.receiptInOutBudgetWidgetTabs = receiptInOutBudgetWidgetTabs;
    this.receiptReceipts = receiptReceipts;
    this.receiptSetting = receiptSetting;
    this.receiptYearMonth = receiptYearMonth;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentReceiptBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentReceiptBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_receipt, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentReceiptBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_Add;
      ImageButton buttonAdd = ViewBindings.findChildViewById(rootView, id);
      if (buttonAdd == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_List;
      RecyclerView receiptDailyInOutList = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutList == null) {
        break missingId;
      }

      id = R.id.receipt_InOut_Budget_Container;
      NestedScrollableHost receiptInOutBudgetContainer = ViewBindings.findChildViewById(rootView, id);
      if (receiptInOutBudgetContainer == null) {
        break missingId;
      }

      id = R.id.receipt_InOut_Budget_Widget;
      ViewPager2 receiptInOutBudgetWidget = ViewBindings.findChildViewById(rootView, id);
      if (receiptInOutBudgetWidget == null) {
        break missingId;
      }

      id = R.id.receipt_InOut_Budget_WidgetTabs;
      TabLayout receiptInOutBudgetWidgetTabs = ViewBindings.findChildViewById(rootView, id);
      if (receiptInOutBudgetWidgetTabs == null) {
        break missingId;
      }

      id = R.id.receipt_Receipts;
      TextView receiptReceipts = ViewBindings.findChildViewById(rootView, id);
      if (receiptReceipts == null) {
        break missingId;
      }

      id = R.id.receipt_Setting;
      ImageView receiptSetting = ViewBindings.findChildViewById(rootView, id);
      if (receiptSetting == null) {
        break missingId;
      }

      id = R.id.receipt_Year_Month;
      TextView receiptYearMonth = ViewBindings.findChildViewById(rootView, id);
      if (receiptYearMonth == null) {
        break missingId;
      }

      return new FragmentReceiptBinding((ConstraintLayout) rootView, buttonAdd,
          receiptDailyInOutList, receiptInOutBudgetContainer, receiptInOutBudgetWidget,
          receiptInOutBudgetWidgetTabs, receiptReceipts, receiptSetting, receiptYearMonth);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
