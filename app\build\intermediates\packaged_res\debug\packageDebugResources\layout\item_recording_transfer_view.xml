<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/recording_page_guideline_vertical_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <!-- 转出账户选择 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/recording_page_transfer_from_account"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="1dp"
        android:background="@drawable/frag_recording_transfer_out_bg"
        android:clipToOutline="true"
        android:paddingStart="18dp"
        android:paddingEnd="18dp"
        app:backgroundTint="@null"
        app:layout_constraintBottom_toTopOf="@id/recording_page_guideline_vertical_center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_recording_page_transfer_from_account_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_recording_page_transfer_from_account_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/recording_transfer_select_transfer_out_account"
            android:textColor="@color/defaultGrey"
            android:textSize="16sp"
            android:maxWidth="500dp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/iv_recording_page_transfer_from_account_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tv_recording_page_transfer_from_account_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/defaultGrey"
            android:textSize="16sp"
            android:layout_gravity="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 转入账户选择 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/recording_page_transfer_to_account"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="1dp"
        android:background="@drawable/frag_recording_transfer_in_bg"
        android:clipToOutline="true"
        android:paddingStart="18dp"
        android:paddingEnd="18dp"
        app:backgroundTint="@null"
        app:layout_constraintTop_toBottomOf="@id/recording_page_guideline_vertical_center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_recording_page_transfer_to_account_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_recording_page_transfer_to_account_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/recording_transfer_select_transfer_in_account"
            android:textColor="@color/defaultGrey"
            android:textSize="16sp"
            android:maxWidth="500dp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/iv_recording_page_transfer_to_account_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tv_recording_page_transfer_to_account_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/defaultGrey"
            android:textSize="16sp"
            android:layout_gravity="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 交换按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_recording_page_transfer_swap_accounts"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="@drawable/frag_recording_transfer_swap_bg"
        android:elevation="0dp"
        android:insetLeft="0dp"
        android:insetTop="0dp"
        android:insetRight="0dp"
        android:insetBottom="0dp"
        app:backgroundTint="@null"
        app:cornerRadius="24dp"
        app:icon="@drawable/icon_swap"
        app:iconGravity="textStart"
        app:iconPadding="0dp"
        app:iconSize="40dp"
        app:iconTint="@color/grey"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rippleColor="@android:color/transparent"
        app:strokeWidth="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>