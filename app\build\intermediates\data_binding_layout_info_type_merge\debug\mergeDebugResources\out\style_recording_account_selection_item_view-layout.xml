<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="style_recording_account_selection_item_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\style_recording_account_selection_item_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/style_recording_account_selection_item_view_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="61" endOffset="14"/></Target><Target id="@+id/iv_recording_page_account_item_icon" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="12" startOffset="4" endLine="20" endOffset="46"/></Target><Target id="@+id/tv_recording_page_account_name" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="37" endOffset="33"/></Target><Target id="@+id/tv_recording_page_account_type" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="47" endOffset="30"/></Target><Target id="@+id/tv_recording_page_account_balance" view="TextView"><Expressions/><location startLine="52" startOffset="4" endLine="59" endOffset="32"/></Target></Targets></Layout>