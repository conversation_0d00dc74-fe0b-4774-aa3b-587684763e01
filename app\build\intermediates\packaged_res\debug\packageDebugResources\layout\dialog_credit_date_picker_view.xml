<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/dialog_bottom_sheet_background"
    android:paddingStart="15dp"
    android:paddingTop="10dp"
    android:paddingEnd="15dp"
    android:paddingBottom="25dp">

    <!-- 标题栏 -->
    <View
        android:id="@+id/drag_handle"
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_marginBottom="16dp"
        android:background="#DDDDDD"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_CreditDatePicker_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="5dp"
        android:background="?attr/selectableItemBackground"
        android:padding="10dp"
        android:text="@string/确定"
        android:textColor="@color/HuaQing"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/drag_handle"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_CreditDatePicker_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:background="?attr/selectableItemBackground"
        android:padding="10dp"
        android:text="@string/取消"
        android:textColor="#666666"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/tv_CreditDatePicker_confirm"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_CreditDatePicker_confirm" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/tv_CreditDatePicker_confirm"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:text="@string/add_new_account_每月"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:layout_gravity="center"
            android:gravity="center" />

        <NumberPicker
            android:id="@+id/number_picker_day"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:layout_gravity="center_horizontal"
            android:theme="@style/DatePickerStyle"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:text="@string/add_new_account_日"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:layout_gravity="center"
            android:gravity="center" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>