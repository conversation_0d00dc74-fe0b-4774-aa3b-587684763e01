<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recording_page"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/YinBai"
    android:fitsSystemWindows="true"
    android:padding="15dp"
    tools:context=".RecordingPageActivity">

    <ImageView
        android:id="@+id/recording_page_close_page"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:contentDescription="@string/recording_page_desc_关闭页面"
        android:padding="15dp"
        android:src="@drawable/icon_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/recording_page_InOutTrans_tabs"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/YinBai"
        android:contentDescription="@string/recording_page_desc_收支转账导航栏"
        app:layout_constraintBottom_toBottomOf="@id/recording_page_close_page"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tabBackground="@null"
        app:tabRippleColor="@null" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/recording_page_InOutTrans_views"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recording_page_close_page"
        app:layout_constraintBottom_toTopOf="@id/recording_page_Select_widget"/>

    <HorizontalScrollView
        android:id="@+id/recording_page_Select_widget"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:orientation="horizontal"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/recording_page_NumAndRemark_widget"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/recording_page_SelectAccount"
                style="@style/记账选项按键样式"
                android:layout_marginEnd="8dp"
                android:text="@string/recording_select_account"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/recording_page_SelectTime"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="TouchTargetSizeCheck" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/recording_page_SelectTime"
                style="@style/记账选项按键样式"
                android:layout_marginEnd="8dp"
                android:clickable="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/recording_page_SelectTags"
                app:layout_constraintStart_toEndOf="@id/recording_page_SelectAccount"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="TouchTargetSizeCheck"
                tools:text="设置时间" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/recording_page_SelectTags"
                style="@style/记账选项按键样式"
                android:layout_marginEnd="8dp"
                android:text="@string/recording_tags"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/recording_page_SelectOthers"
                app:layout_constraintStart_toEndOf="@id/recording_page_SelectTime"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="TouchTargetSizeCheck" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/recording_page_SelectOthers"
                style="@style/记账选项按键样式"
                android:text="@string/recording_labels"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/recording_page_SelectTags"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="TouchTargetSizeCheck" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </HorizontalScrollView>

    <LinearLayout
        android:id="@+id/recording_page_NumAndRemark_widget"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/widget_common_bg"
        android:divider="@drawable/widget_divider"
        android:dividerPadding="15dp"
        android:orientation="vertical"
        android:showDividers="middle"
        app:layout_constraintBottom_toTopOf="@id/recording_page_inputKeyboard">

        <!-- 数字运算显示框 -->
        <TextView
            android:id="@+id/tv_recording_page_recordingNum"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="15dp"
            android:paddingBottom="10dp"
            android:text="0.00"
            android:textAlignment="viewEnd"
            android:textColor="@color/ChaHuaHong"
            android:textSize="32sp"
            android:textStyle="bold" />

        <!-- 备注输入框 -->
        <EditText
            android:id="@+id/et_recording_page_remark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:hint="@string/recording_输入收支备注"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="15dp"
            android:paddingBottom="10dp"
            android:textSize="16sp"
            tools:ignore="TouchTargetSizeCheck" />

    </LinearLayout>

    <!-- 数字键盘 -->
    <GridLayout
        android:id="@+id/recording_page_inputKeyboard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="15dp"
        android:alignmentMode="alignBounds"
        android:columnCount="4"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:rowCount="4"
        android:useDefaultMargins="false"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 第一行 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn1"
            style="@style/软键盘按键样式"
            android:layout_marginEnd="3dp"
            android:layout_marginBottom="3dp"
            android:text="@string/inputKeyboard_1" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn2"
            style="@style/软键盘按键样式"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="3dp"
            android:layout_marginBottom="3dp"
            android:text="@string/inputKeyboard_2" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn3"
            style="@style/软键盘按键样式"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="3dp"
            android:layout_marginBottom="3dp"
            android:text="@string/inputKeyboard_3" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnBackspace"
            style="@style/软键盘按键样式"
            android:layout_marginStart="3dp"
            android:layout_marginBottom="3dp"
            android:text="←" />

        <!-- 第二行 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn4"
            style="@style/软键盘按键样式"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="3dp"
            android:layout_marginBottom="3dp"
            android:text="@string/inputKeyboard_4" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn5"
            style="@style/软键盘按键样式"
            android:layout_margin="3dp"
            android:text="@string/inputKeyboard_5" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn6"
            style="@style/软键盘按键样式"
            android:layout_margin="3dp"
            android:text="@string/inputKeyboard_6" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnPlus"
            style="@style/软键盘按键样式"
            android:layout_marginStart="3dp"
            android:layout_marginTop="3dp"
            android:layout_marginBottom="3dp"
            android:text="@string/inputKeyboard_plus" />

        <!-- 第三行 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn7"
            style="@style/软键盘按键样式"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="3dp"
            android:layout_marginBottom="3dp"
            android:text="@string/inputKeyboard_7" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn8"
            style="@style/软键盘按键样式"
            android:layout_margin="3dp"
            android:text="@string/inputKeyboard_8" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn9"
            style="@style/软键盘按键样式"
            android:layout_margin="3dp"
            android:text="@string/inputKeyboard_9" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnMinus"
            style="@style/软键盘按键样式"
            android:layout_marginStart="3dp"
            android:layout_marginTop="3dp"
            android:layout_marginBottom="3dp"
            android:text="@string/inputKeyboard_minus" />

        <!-- 第四行 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnAddAnother"
            style="@style/软键盘按键样式"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="3dp"
            android:text="@string/inputKeyboard_AddAnother" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn0"
            style="@style/软键盘按键样式"
            android:layout_marginStart="3dp"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="3dp"
            android:text="@string/inputKeyboard_0" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnDot"
            style="@style/软键盘按键样式"
            android:layout_marginStart="3dp"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="3dp"
            android:text="@string/inputKeyboard_dot" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnEqual"
            style="@style/软键盘按键样式"
            android:layout_marginStart="3dp"
            android:layout_marginTop="3dp"
            android:text="@string/inputKeyboard_confirm"
            android:textColor="@color/white"
            app:backgroundTint="@color/ChaHuaHong" />
    </GridLayout>

</androidx.constraintlayout.widget.ConstraintLayout>