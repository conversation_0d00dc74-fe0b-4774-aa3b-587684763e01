// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddNewAccountListViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final TextView walletsAccountBack;

  @NonNull
  public final RecyclerView walletsAddAccountType;

  @NonNull
  public final LinearLayout walletsAddNewAccount;

  @NonNull
  public final TextView walletsAddTitle;

  private DialogAddNewAccountListViewBinding(@NonNull LinearLayout rootView,
      @NonNull View dragHandle, @NonNull TextView walletsAccountBack,
      @NonNull RecyclerView walletsAddAccountType, @NonNull LinearLayout walletsAddNewAccount,
      @NonNull TextView walletsAddTitle) {
    this.rootView = rootView;
    this.dragHandle = dragHandle;
    this.walletsAccountBack = walletsAccountBack;
    this.walletsAddAccountType = walletsAddAccountType;
    this.walletsAddNewAccount = walletsAddNewAccount;
    this.walletsAddTitle = walletsAddTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddNewAccountListViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddNewAccountListViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_new_account_list_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddNewAccountListViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.wallets_account_back;
      TextView walletsAccountBack = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountBack == null) {
        break missingId;
      }

      id = R.id.wallets_add_account_type;
      RecyclerView walletsAddAccountType = ViewBindings.findChildViewById(rootView, id);
      if (walletsAddAccountType == null) {
        break missingId;
      }

      LinearLayout walletsAddNewAccount = (LinearLayout) rootView;

      id = R.id.wallets_add_title;
      TextView walletsAddTitle = ViewBindings.findChildViewById(rootView, id);
      if (walletsAddTitle == null) {
        break missingId;
      }

      return new DialogAddNewAccountListViewBinding((LinearLayout) rootView, dragHandle,
          walletsAccountBack, walletsAddAccountType, walletsAddNewAccount, walletsAddTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
