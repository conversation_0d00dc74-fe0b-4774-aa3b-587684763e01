// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.NumberPicker;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCreditDatePickerViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final NumberPicker numberPickerDay;

  @NonNull
  public final TextView tvCreditDatePickerCancel;

  @NonNull
  public final TextView tvCreditDatePickerConfirm;

  private DialogCreditDatePickerViewBinding(@NonNull ConstraintLayout rootView,
      @NonNull View dragHandle, @NonNull NumberPicker numberPickerDay,
      @NonNull TextView tvCreditDatePickerCancel, @NonNull TextView tvCreditDatePickerConfirm) {
    this.rootView = rootView;
    this.dragHandle = dragHandle;
    this.numberPickerDay = numberPickerDay;
    this.tvCreditDatePickerCancel = tvCreditDatePickerCancel;
    this.tvCreditDatePickerConfirm = tvCreditDatePickerConfirm;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCreditDatePickerViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCreditDatePickerViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_credit_date_picker_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCreditDatePickerViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.number_picker_day;
      NumberPicker numberPickerDay = ViewBindings.findChildViewById(rootView, id);
      if (numberPickerDay == null) {
        break missingId;
      }

      id = R.id.tv_CreditDatePicker_cancel;
      TextView tvCreditDatePickerCancel = ViewBindings.findChildViewById(rootView, id);
      if (tvCreditDatePickerCancel == null) {
        break missingId;
      }

      id = R.id.tv_CreditDatePicker_confirm;
      TextView tvCreditDatePickerConfirm = ViewBindings.findChildViewById(rootView, id);
      if (tvCreditDatePickerConfirm == null) {
        break missingId;
      }

      return new DialogCreditDatePickerViewBinding((ConstraintLayout) rootView, dragHandle,
          numberPickerDay, tvCreditDatePickerCancel, tvCreditDatePickerConfirm);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
