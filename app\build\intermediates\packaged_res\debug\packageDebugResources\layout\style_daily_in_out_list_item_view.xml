<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/receipt_Daily_InOut_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="02/06 星期四" />

    <TextView
        android:id="@+id/receipt_Daily_InOut_stats"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="收入：¥200.00 支出：¥300.00"
        android:textSize="12sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/receipt_Daily_InOut_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/widget_common_bg"
        android:backgroundTint="@color/white"
        android:scrollbars="none"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/receipt_Daily_InOut_date" />

</androidx.constraintlayout.widget.ConstraintLayout>