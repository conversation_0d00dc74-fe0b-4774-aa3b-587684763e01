<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_recording_account_selection_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_recording_account_selection_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_recording_account_selection_view_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="62" endOffset="14"/></Target><Target id="@+id/drag_handle" view="View"><Expressions/><location startLine="13" startOffset="4" endLine="19" endOffset="38"/></Target><Target id="@+id/recording_page_account_selection_back" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="36" endOffset="96"/></Target><Target id="@+id/recording_page_account_selection_title" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="49" endOffset="55"/></Target><Target id="@+id/recording_page_account_selection_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="54" startOffset="4" endLine="60" endOffset="71"/></Target></Targets></Layout>