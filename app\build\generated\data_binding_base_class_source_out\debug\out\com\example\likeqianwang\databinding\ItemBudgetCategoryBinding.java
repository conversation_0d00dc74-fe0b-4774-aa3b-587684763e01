// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBudgetCategoryBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final EditText budgetCategoryAlertThreshold;

  @NonNull
  public final EditText budgetCategoryAmount;

  @NonNull
  public final LinearLayout budgetCategoryAmountLayout;

  @NonNull
  public final ImageView budgetCategoryDelete;

  @NonNull
  public final ImageView budgetCategoryIcon;

  @NonNull
  public final TextView budgetCategoryName;

  private ItemBudgetCategoryBinding(@NonNull ConstraintLayout rootView,
      @NonNull EditText budgetCategoryAlertThreshold, @NonNull EditText budgetCategoryAmount,
      @NonNull LinearLayout budgetCategoryAmountLayout, @NonNull ImageView budgetCategoryDelete,
      @NonNull ImageView budgetCategoryIcon, @NonNull TextView budgetCategoryName) {
    this.rootView = rootView;
    this.budgetCategoryAlertThreshold = budgetCategoryAlertThreshold;
    this.budgetCategoryAmount = budgetCategoryAmount;
    this.budgetCategoryAmountLayout = budgetCategoryAmountLayout;
    this.budgetCategoryDelete = budgetCategoryDelete;
    this.budgetCategoryIcon = budgetCategoryIcon;
    this.budgetCategoryName = budgetCategoryName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBudgetCategoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBudgetCategoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_budget_category, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBudgetCategoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.budget_category_alert_threshold;
      EditText budgetCategoryAlertThreshold = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategoryAlertThreshold == null) {
        break missingId;
      }

      id = R.id.budget_category_amount;
      EditText budgetCategoryAmount = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategoryAmount == null) {
        break missingId;
      }

      id = R.id.budget_category_amount_layout;
      LinearLayout budgetCategoryAmountLayout = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategoryAmountLayout == null) {
        break missingId;
      }

      id = R.id.budget_category_delete;
      ImageView budgetCategoryDelete = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategoryDelete == null) {
        break missingId;
      }

      id = R.id.budget_category_icon;
      ImageView budgetCategoryIcon = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategoryIcon == null) {
        break missingId;
      }

      id = R.id.budget_category_name;
      TextView budgetCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategoryName == null) {
        break missingId;
      }

      return new ItemBudgetCategoryBinding((ConstraintLayout) rootView,
          budgetCategoryAlertThreshold, budgetCategoryAmount, budgetCategoryAmountLayout,
          budgetCategoryDelete, budgetCategoryIcon, budgetCategoryName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
