package com.example.likeqianwang.Dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Entity.TransactionTagCrossRef;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TransactionTagCrossRefDao_Impl implements TransactionTagCrossRefDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TransactionTagCrossRef> __insertionAdapterOfTransactionTagCrossRef;

  private final EntityDeletionOrUpdateAdapter<TransactionTagCrossRef> __deletionAdapterOfTransactionTagCrossRef;

  private final EntityDeletionOrUpdateAdapter<TransactionTagCrossRef> __updateAdapterOfTransactionTagCrossRef;

  private final SharedSQLiteStatement __preparedStmtOfDeleteTagsForRecord;

  public TransactionTagCrossRefDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTransactionTagCrossRef = new EntityInsertionAdapter<TransactionTagCrossRef>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `transaction_tag_cross_ref` (`transactionId`,`tagId`) VALUES (?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionTagCrossRef entity) {
        statement.bindLong(1, entity.getTransactionId());
        statement.bindLong(2, entity.getTagId());
      }
    };
    this.__deletionAdapterOfTransactionTagCrossRef = new EntityDeletionOrUpdateAdapter<TransactionTagCrossRef>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `transaction_tag_cross_ref` WHERE `transactionId` = ? AND `tagId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionTagCrossRef entity) {
        statement.bindLong(1, entity.getTransactionId());
        statement.bindLong(2, entity.getTagId());
      }
    };
    this.__updateAdapterOfTransactionTagCrossRef = new EntityDeletionOrUpdateAdapter<TransactionTagCrossRef>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `transaction_tag_cross_ref` SET `transactionId` = ?,`tagId` = ? WHERE `transactionId` = ? AND `tagId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionTagCrossRef entity) {
        statement.bindLong(1, entity.getTransactionId());
        statement.bindLong(2, entity.getTagId());
        statement.bindLong(3, entity.getTransactionId());
        statement.bindLong(4, entity.getTagId());
      }
    };
    this.__preparedStmtOfDeleteTagsForRecord = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM transaction_tag_cross_ref WHERE transactionId = ?";
        return _query;
      }
    };
  }

  @Override
  public void insert(final TransactionTagCrossRef join) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfTransactionTagCrossRef.insert(join);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final TransactionTagCrossRef join) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfTransactionTagCrossRef.handle(join);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final TransactionTagCrossRef join) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfTransactionTagCrossRef.handle(join);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void deleteTagsForRecord(final int transactionId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteTagsForRecord.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, transactionId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteTagsForRecord.release(_stmt);
    }
  }

  @Override
  public List<TransactionTag> getTagsForTransaction(final long transactionId) {
    final String _sql = "SELECT t.* FROM transaction_tags t INNER JOIN transaction_tag_cross_ref tc ON t.tagId = tc.tagId WHERE tc.transactionId = ? ORDER BY t.order_index ASC, t.tag_name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, transactionId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfTagId = CursorUtil.getColumnIndexOrThrow(_cursor, "tagId");
      final int _cursorIndexOfTagName = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_name");
      final int _cursorIndexOfTagColor = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_color");
      final int _cursorIndexOfTagCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_category");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
      final int _cursorIndexOfIsSelected = CursorUtil.getColumnIndexOrThrow(_cursor, "is_selected");
      final List<TransactionTag> _result = new ArrayList<TransactionTag>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final TransactionTag _item;
        _item = new TransactionTag();
        final long _tmpTagId;
        _tmpTagId = _cursor.getLong(_cursorIndexOfTagId);
        _item.setTagId(_tmpTagId);
        final String _tmpTagName;
        if (_cursor.isNull(_cursorIndexOfTagName)) {
          _tmpTagName = null;
        } else {
          _tmpTagName = _cursor.getString(_cursorIndexOfTagName);
        }
        _item.setTagName(_tmpTagName);
        final String _tmpTagColor;
        if (_cursor.isNull(_cursorIndexOfTagColor)) {
          _tmpTagColor = null;
        } else {
          _tmpTagColor = _cursor.getString(_cursorIndexOfTagColor);
        }
        _item.setTagColor(_tmpTagColor);
        final String _tmpTagCategory;
        if (_cursor.isNull(_cursorIndexOfTagCategory)) {
          _tmpTagCategory = null;
        } else {
          _tmpTagCategory = _cursor.getString(_cursorIndexOfTagCategory);
        }
        _item.setTagCategory(_tmpTagCategory);
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _item.setOrderIndex(_tmpOrderIndex);
        final boolean _tmpIsSelected;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSelected);
        _tmpIsSelected = _tmp != 0;
        _item.setSelected(_tmpIsSelected);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
