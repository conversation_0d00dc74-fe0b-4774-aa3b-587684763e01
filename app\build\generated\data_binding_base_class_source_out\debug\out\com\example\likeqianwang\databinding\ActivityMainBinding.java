// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.example.likeqianwang.R;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout container;

  @NonNull
  public final ViewPager2 initViewPagerFragments;

  @NonNull
  public final TabLayout initViewPagerTabs;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout container, @NonNull ViewPager2 initViewPagerFragments,
      @NonNull TabLayout initViewPagerTabs) {
    this.rootView = rootView;
    this.container = container;
    this.initViewPagerFragments = initViewPagerFragments;
    this.initViewPagerTabs = initViewPagerTabs;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ConstraintLayout container = (ConstraintLayout) rootView;

      id = R.id.initViewPager_fragments;
      ViewPager2 initViewPagerFragments = ViewBindings.findChildViewById(rootView, id);
      if (initViewPagerFragments == null) {
        break missingId;
      }

      id = R.id.initViewPager_tabs;
      TabLayout initViewPagerTabs = ViewBindings.findChildViewById(rootView, id);
      if (initViewPagerTabs == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, container, initViewPagerFragments,
          initViewPagerTabs);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
