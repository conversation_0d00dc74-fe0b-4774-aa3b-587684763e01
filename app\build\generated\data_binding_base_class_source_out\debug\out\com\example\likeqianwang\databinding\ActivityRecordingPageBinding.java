// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.GridLayout;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.example.likeqianwang.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRecordingPageBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btn0;

  @NonNull
  public final MaterialButton btn1;

  @NonNull
  public final MaterialButton btn2;

  @NonNull
  public final MaterialButton btn3;

  @NonNull
  public final MaterialButton btn4;

  @NonNull
  public final MaterialButton btn5;

  @NonNull
  public final MaterialButton btn6;

  @NonNull
  public final MaterialButton btn7;

  @NonNull
  public final MaterialButton btn8;

  @NonNull
  public final MaterialButton btn9;

  @NonNull
  public final MaterialButton btnAddAnother;

  @NonNull
  public final MaterialButton btnBackspace;

  @NonNull
  public final MaterialButton btnDot;

  @NonNull
  public final MaterialButton btnEqual;

  @NonNull
  public final MaterialButton btnMinus;

  @NonNull
  public final MaterialButton btnPlus;

  @NonNull
  public final EditText etRecordingPageRemark;

  @NonNull
  public final ConstraintLayout recordingPage;

  @NonNull
  public final ImageView recordingPageClosePage;

  @NonNull
  public final TabLayout recordingPageInOutTransTabs;

  @NonNull
  public final ViewPager2 recordingPageInOutTransViews;

  @NonNull
  public final GridLayout recordingPageInputKeyboard;

  @NonNull
  public final LinearLayout recordingPageNumAndRemarkWidget;

  @NonNull
  public final MaterialButton recordingPageSelectAccount;

  @NonNull
  public final MaterialButton recordingPageSelectOthers;

  @NonNull
  public final MaterialButton recordingPageSelectTags;

  @NonNull
  public final MaterialButton recordingPageSelectTime;

  @NonNull
  public final HorizontalScrollView recordingPageSelectWidget;

  @NonNull
  public final TextView tvRecordingPageRecordingNum;

  private ActivityRecordingPageBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btn0, @NonNull MaterialButton btn1, @NonNull MaterialButton btn2,
      @NonNull MaterialButton btn3, @NonNull MaterialButton btn4, @NonNull MaterialButton btn5,
      @NonNull MaterialButton btn6, @NonNull MaterialButton btn7, @NonNull MaterialButton btn8,
      @NonNull MaterialButton btn9, @NonNull MaterialButton btnAddAnother,
      @NonNull MaterialButton btnBackspace, @NonNull MaterialButton btnDot,
      @NonNull MaterialButton btnEqual, @NonNull MaterialButton btnMinus,
      @NonNull MaterialButton btnPlus, @NonNull EditText etRecordingPageRemark,
      @NonNull ConstraintLayout recordingPage, @NonNull ImageView recordingPageClosePage,
      @NonNull TabLayout recordingPageInOutTransTabs,
      @NonNull ViewPager2 recordingPageInOutTransViews,
      @NonNull GridLayout recordingPageInputKeyboard,
      @NonNull LinearLayout recordingPageNumAndRemarkWidget,
      @NonNull MaterialButton recordingPageSelectAccount,
      @NonNull MaterialButton recordingPageSelectOthers,
      @NonNull MaterialButton recordingPageSelectTags,
      @NonNull MaterialButton recordingPageSelectTime,
      @NonNull HorizontalScrollView recordingPageSelectWidget,
      @NonNull TextView tvRecordingPageRecordingNum) {
    this.rootView = rootView;
    this.btn0 = btn0;
    this.btn1 = btn1;
    this.btn2 = btn2;
    this.btn3 = btn3;
    this.btn4 = btn4;
    this.btn5 = btn5;
    this.btn6 = btn6;
    this.btn7 = btn7;
    this.btn8 = btn8;
    this.btn9 = btn9;
    this.btnAddAnother = btnAddAnother;
    this.btnBackspace = btnBackspace;
    this.btnDot = btnDot;
    this.btnEqual = btnEqual;
    this.btnMinus = btnMinus;
    this.btnPlus = btnPlus;
    this.etRecordingPageRemark = etRecordingPageRemark;
    this.recordingPage = recordingPage;
    this.recordingPageClosePage = recordingPageClosePage;
    this.recordingPageInOutTransTabs = recordingPageInOutTransTabs;
    this.recordingPageInOutTransViews = recordingPageInOutTransViews;
    this.recordingPageInputKeyboard = recordingPageInputKeyboard;
    this.recordingPageNumAndRemarkWidget = recordingPageNumAndRemarkWidget;
    this.recordingPageSelectAccount = recordingPageSelectAccount;
    this.recordingPageSelectOthers = recordingPageSelectOthers;
    this.recordingPageSelectTags = recordingPageSelectTags;
    this.recordingPageSelectTime = recordingPageSelectTime;
    this.recordingPageSelectWidget = recordingPageSelectWidget;
    this.tvRecordingPageRecordingNum = tvRecordingPageRecordingNum;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRecordingPageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRecordingPageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_recording_page, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRecordingPageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn0;
      MaterialButton btn0 = ViewBindings.findChildViewById(rootView, id);
      if (btn0 == null) {
        break missingId;
      }

      id = R.id.btn1;
      MaterialButton btn1 = ViewBindings.findChildViewById(rootView, id);
      if (btn1 == null) {
        break missingId;
      }

      id = R.id.btn2;
      MaterialButton btn2 = ViewBindings.findChildViewById(rootView, id);
      if (btn2 == null) {
        break missingId;
      }

      id = R.id.btn3;
      MaterialButton btn3 = ViewBindings.findChildViewById(rootView, id);
      if (btn3 == null) {
        break missingId;
      }

      id = R.id.btn4;
      MaterialButton btn4 = ViewBindings.findChildViewById(rootView, id);
      if (btn4 == null) {
        break missingId;
      }

      id = R.id.btn5;
      MaterialButton btn5 = ViewBindings.findChildViewById(rootView, id);
      if (btn5 == null) {
        break missingId;
      }

      id = R.id.btn6;
      MaterialButton btn6 = ViewBindings.findChildViewById(rootView, id);
      if (btn6 == null) {
        break missingId;
      }

      id = R.id.btn7;
      MaterialButton btn7 = ViewBindings.findChildViewById(rootView, id);
      if (btn7 == null) {
        break missingId;
      }

      id = R.id.btn8;
      MaterialButton btn8 = ViewBindings.findChildViewById(rootView, id);
      if (btn8 == null) {
        break missingId;
      }

      id = R.id.btn9;
      MaterialButton btn9 = ViewBindings.findChildViewById(rootView, id);
      if (btn9 == null) {
        break missingId;
      }

      id = R.id.btnAddAnother;
      MaterialButton btnAddAnother = ViewBindings.findChildViewById(rootView, id);
      if (btnAddAnother == null) {
        break missingId;
      }

      id = R.id.btnBackspace;
      MaterialButton btnBackspace = ViewBindings.findChildViewById(rootView, id);
      if (btnBackspace == null) {
        break missingId;
      }

      id = R.id.btnDot;
      MaterialButton btnDot = ViewBindings.findChildViewById(rootView, id);
      if (btnDot == null) {
        break missingId;
      }

      id = R.id.btnEqual;
      MaterialButton btnEqual = ViewBindings.findChildViewById(rootView, id);
      if (btnEqual == null) {
        break missingId;
      }

      id = R.id.btnMinus;
      MaterialButton btnMinus = ViewBindings.findChildViewById(rootView, id);
      if (btnMinus == null) {
        break missingId;
      }

      id = R.id.btnPlus;
      MaterialButton btnPlus = ViewBindings.findChildViewById(rootView, id);
      if (btnPlus == null) {
        break missingId;
      }

      id = R.id.et_recording_page_remark;
      EditText etRecordingPageRemark = ViewBindings.findChildViewById(rootView, id);
      if (etRecordingPageRemark == null) {
        break missingId;
      }

      ConstraintLayout recordingPage = (ConstraintLayout) rootView;

      id = R.id.recording_page_close_page;
      ImageView recordingPageClosePage = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageClosePage == null) {
        break missingId;
      }

      id = R.id.recording_page_InOutTrans_tabs;
      TabLayout recordingPageInOutTransTabs = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageInOutTransTabs == null) {
        break missingId;
      }

      id = R.id.recording_page_InOutTrans_views;
      ViewPager2 recordingPageInOutTransViews = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageInOutTransViews == null) {
        break missingId;
      }

      id = R.id.recording_page_inputKeyboard;
      GridLayout recordingPageInputKeyboard = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageInputKeyboard == null) {
        break missingId;
      }

      id = R.id.recording_page_NumAndRemark_widget;
      LinearLayout recordingPageNumAndRemarkWidget = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageNumAndRemarkWidget == null) {
        break missingId;
      }

      id = R.id.recording_page_SelectAccount;
      MaterialButton recordingPageSelectAccount = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageSelectAccount == null) {
        break missingId;
      }

      id = R.id.recording_page_SelectOthers;
      MaterialButton recordingPageSelectOthers = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageSelectOthers == null) {
        break missingId;
      }

      id = R.id.recording_page_SelectTags;
      MaterialButton recordingPageSelectTags = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageSelectTags == null) {
        break missingId;
      }

      id = R.id.recording_page_SelectTime;
      MaterialButton recordingPageSelectTime = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageSelectTime == null) {
        break missingId;
      }

      id = R.id.recording_page_Select_widget;
      HorizontalScrollView recordingPageSelectWidget = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageSelectWidget == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_recordingNum;
      TextView tvRecordingPageRecordingNum = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageRecordingNum == null) {
        break missingId;
      }

      return new ActivityRecordingPageBinding((ConstraintLayout) rootView, btn0, btn1, btn2, btn3,
          btn4, btn5, btn6, btn7, btn8, btn9, btnAddAnother, btnBackspace, btnDot, btnEqual,
          btnMinus, btnPlus, etRecordingPageRemark, recordingPage, recordingPageClosePage,
          recordingPageInOutTransTabs, recordingPageInOutTransViews, recordingPageInputKeyboard,
          recordingPageNumAndRemarkWidget, recordingPageSelectAccount, recordingPageSelectOthers,
          recordingPageSelectTags, recordingPageSelectTime, recordingPageSelectWidget,
          tvRecordingPageRecordingNum);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
