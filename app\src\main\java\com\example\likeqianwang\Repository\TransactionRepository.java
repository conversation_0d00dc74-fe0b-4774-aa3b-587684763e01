package com.example.likeqianwang.Repository;

import android.util.Log;

import androidx.room.Transaction;

import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Dao.TransactionCategoryDao;
import com.example.likeqianwang.Dao.TransactionTagCrossRefDao;
import com.example.likeqianwang.Dao.TransactionTagDao;
import com.example.likeqianwang.Dao.TransactionDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Entity.TransactionTagCrossRef;
import com.example.likeqianwang.Entity.Transactions;

import java.math.BigDecimal;
import java.util.List;

public class TransactionRepository {
    private final AccountDao accountDao;
    private final TransactionDao transactionDao;
    private final TransactionCategoryDao categoryDao;
    private final TransactionTagDao transactionTagDao;
    private final TransactionTagCrossRefDao transactionTagCrossRefDao;

    public TransactionRepository(AppDatabase database) {
        this.transactionDao = database.transactionDao();
        this.accountDao = database.accountDao();
        this.categoryDao = database.transactionCategoryDao();
        this.transactionTagDao = database.transactionTagDao();
        this.transactionTagCrossRefDao = database.transactionTagCrossRefDao();
    }

    @Transaction
    public long addTransaction(Transactions transaction, List<TransactionTag> tags) {
        Log.d("TransactionRepository", "addTransaction called");
        Log.d("TransactionRepository", "Transaction type: " + transaction.getType());
        Log.d("TransactionRepository", "FromAccountId: " + transaction.getFromAccountId());
        Log.d("TransactionRepository", "ToAccountId: " + transaction.getToAccountId());

        try {
            // 0. 验证账户存在性（在插入之前）
            Log.d("TransactionRepository", "Validating account existence...");
            validateAccountsExist(transaction);
            Log.d("TransactionRepository", "Account validation passed");

            // 1. 插入交易记录
            Log.d("TransactionRepository", "Inserting transaction...");
            long transactionId = transactionDao.insert(transaction);
            Log.d("TransactionRepository", "Transaction inserted with ID: " + transactionId);

            // 2. 更新账户余额
            Log.d("TransactionRepository", "Updating account balances...");
            updateAccountBalances(transaction);
            Log.d("TransactionRepository", "Account balances updated");

            // 3. 保存标签关联
            if (tags != null && !tags.isEmpty()) {
                Log.d("TransactionRepository", "Saving " + tags.size() + " tag associations...");
                for (TransactionTag tag : tags) {
                    TransactionTagCrossRef crossRef = new TransactionTagCrossRef();
                    crossRef.setTransactionId(transactionId);
                    crossRef.setTagId(tag.getTagId());
                    transactionTagCrossRefDao.insert(crossRef);
                }
                Log.d("TransactionRepository", "Tag associations saved");
            } else {
                Log.d("TransactionRepository", "No tags to save");
            }

            Log.d("TransactionRepository", "Transaction save completed successfully");
            return transactionId;
        } catch (Exception e) {
            Log.e("TransactionRepository", "Error saving transaction", e);
            throw e;
        }
    }

    private void updateAccountBalances(Transactions transaction) {
        String type = transaction.getType();
        BigDecimal amount = transaction.getAmount();

        Log.d("TransactionRepository", "updateAccountBalances - Type: " + type + ", Amount: " + amount);

        if ("TRANSFER".equals(type)) {
            // 转账：从转出账户减少，向转入账户增加
            String fromAccountId = transaction.getFromAccountId();
            String toAccountId = transaction.getToAccountId();

            Log.d("TransactionRepository", "Transfer - FromAccountId: " + fromAccountId + ", ToAccountId: " + toAccountId);

            if (fromAccountId == null || fromAccountId.trim().isEmpty()) {
                throw new IllegalArgumentException("转出账户ID不能为空");
            }

            if (toAccountId == null || toAccountId.trim().isEmpty()) {
                throw new IllegalArgumentException("转入账户ID不能为空");
            }

            // 更新转出账户
            Account fromAccount = accountDao.getAccountByIdSync(fromAccountId);
            if (fromAccount == null) {
                throw new IllegalArgumentException("转出账户不存在: " + fromAccountId);
            }

            double fromOldBalance = fromAccount.getAccountBalance();
            double fromNewBalance = fromOldBalance - amount.doubleValue();

            Log.d("TransactionRepository", "FromAccount: " + fromAccount.getAccountName() +
                  ", OldBalance: " + fromOldBalance + ", NewBalance: " + fromNewBalance);

            if (fromNewBalance < 0) {
                Log.w("TransactionRepository", "Warning: FromAccount balance will be negative: " + fromNewBalance);
            }

            fromAccount.setAccountBalance(fromNewBalance);
            accountDao.update(fromAccount);
            Log.d("TransactionRepository", "FromAccount balance updated");

            // 更新转入账户
            Account toAccount = accountDao.getAccountByIdSync(toAccountId);
            if (toAccount == null) {
                throw new IllegalArgumentException("转入账户不存在: " + toAccountId);
            }

            double toOldBalance = toAccount.getAccountBalance();
            double toNewBalance = toOldBalance + amount.doubleValue();

            Log.d("TransactionRepository", "ToAccount: " + toAccount.getAccountName() +
                  ", OldBalance: " + toOldBalance + ", NewBalance: " + toNewBalance);

            toAccount.setAccountBalance(toNewBalance);
            accountDao.update(toAccount);
            Log.d("TransactionRepository", "ToAccount balance updated");

        } else {
            // 收入或支出：更新单个账户余额
            String accountId = transaction.getFromAccountId();

            if (accountId == null || accountId.trim().isEmpty()) {
                throw new IllegalArgumentException("账户ID不能为空");
            }

            Account account = accountDao.getAccountByIdSync(accountId);
            if (account == null) {
                throw new IllegalArgumentException("账户不存在: " + accountId);
            }

            double oldBalance = account.getAccountBalance();
            double newBalance;
            if ("INCOME".equals(type)) {
                // 收入：增加余额
                newBalance = oldBalance + amount.doubleValue();
            } else {
                // 支出：减少余额
                newBalance = oldBalance - amount.doubleValue();
                if (newBalance < 0) {
                    Log.w("TransactionRepository", "Warning: Account balance will be negative: " + newBalance);
                }
            }

            Log.d("TransactionRepository", "Account: " + account.getAccountName() +
                  ", Type: " + type + ", OldBalance: " + oldBalance + ", NewBalance: " + newBalance);

            account.setAccountBalance(newBalance);
            accountDao.update(account);
            Log.d("TransactionRepository", "Account balance updated");
        }
    }

    /**
     * 验证交易中涉及的账户是否存在
     */
    private void validateAccountsExist(Transactions transaction) {
        String type = transaction.getType();
        String fromAccountId = transaction.getFromAccountId();
        String toAccountId = transaction.getToAccountId();

        Log.d("TransactionRepository", "validateAccountsExist - Type: " + type);

        // 验证转出账户（所有类型都需要）
        if (fromAccountId == null || fromAccountId.trim().isEmpty()) {
            throw new IllegalArgumentException("转出账户ID不能为空");
        }

        Account fromAccount = accountDao.getAccountByIdSync(fromAccountId);
        if (fromAccount == null) {
            throw new IllegalArgumentException("转出账户不存在: " + fromAccountId);
        }
        Log.d("TransactionRepository", "FromAccount exists: " + fromAccount.getAccountName());

        // 验证转入账户（仅转账需要）
        if ("TRANSFER".equals(type)) {
            if (toAccountId == null || toAccountId.trim().isEmpty()) {
                throw new IllegalArgumentException("转入账户ID不能为空");
            }

            Account toAccount = accountDao.getAccountByIdSync(toAccountId);
            if (toAccount == null) {
                throw new IllegalArgumentException("转入账户不存在: " + toAccountId);
            }
            Log.d("TransactionRepository", "ToAccount exists: " + toAccount.getAccountName());

            // 验证转出转入账户不能相同
            if (fromAccountId.equals(toAccountId)) {
                throw new IllegalArgumentException("转出账户和转入账户不能相同");
            }
        } else {
            // 非转账交易，确保toAccountId为null
            if (toAccountId != null) {
                Log.w("TransactionRepository", "Non-transfer transaction has toAccountId, setting to null");
                transaction.setToAccountId(null);
            }
        }
    }
}
