// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleAccountBankItemViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ShapeableImageView ivWalletsAccountBankIcon;

  @NonNull
  public final TextView tvWalletsAccountBankName;

  private StyleAccountBankItemViewBinding(@NonNull LinearLayout rootView,
      @NonNull ShapeableImageView ivWalletsAccountBankIcon,
      @NonNull TextView tvWalletsAccountBankName) {
    this.rootView = rootView;
    this.ivWalletsAccountBankIcon = ivWalletsAccountBankIcon;
    this.tvWalletsAccountBankName = tvWalletsAccountBankName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleAccountBankItemViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleAccountBankItemViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_account_bank_item_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleAccountBankItemViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_wallets_account_bank_icon;
      ShapeableImageView ivWalletsAccountBankIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivWalletsAccountBankIcon == null) {
        break missingId;
      }

      id = R.id.tv_wallets_account_bank_name;
      TextView tvWalletsAccountBankName = ViewBindings.findChildViewById(rootView, id);
      if (tvWalletsAccountBankName == null) {
        break missingId;
      }

      return new StyleAccountBankItemViewBinding((LinearLayout) rootView, ivWalletsAccountBankIcon,
          tvWalletsAccountBankName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
