<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_budget_category_selection" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\item_budget_category_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_budget_category_selection_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="43" endOffset="51"/></Target><Target id="@+id/category_selection_icon" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="48"/></Target><Target id="@+id/category_selection_name" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="31" endOffset="25"/></Target><Target id="@+id/category_selection_checkbox" view="CheckBox"><Expressions/><location startLine="33" startOffset="4" endLine="41" endOffset="51"/></Target></Targets></Layout>