// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleDailyInOutDetailTagViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout receiptDailyInOutTagStyle;

  @NonNull
  public final TextView receiptDailyInOutTags;

  private StyleDailyInOutDetailTagViewBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout receiptDailyInOutTagStyle,
      @NonNull TextView receiptDailyInOutTags) {
    this.rootView = rootView;
    this.receiptDailyInOutTagStyle = receiptDailyInOutTagStyle;
    this.receiptDailyInOutTags = receiptDailyInOutTags;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleDailyInOutDetailTagViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleDailyInOutDetailTagViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_daily_in_out_detail_tag_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleDailyInOutDetailTagViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ConstraintLayout receiptDailyInOutTagStyle = (ConstraintLayout) rootView;

      id = R.id.receipt_Daily_InOut_tags;
      TextView receiptDailyInOutTags = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutTags == null) {
        break missingId;
      }

      return new StyleDailyInOutDetailTagViewBinding((ConstraintLayout) rootView,
          receiptDailyInOutTagStyle, receiptDailyInOutTags);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
