// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBudgetCategorySelectionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final CheckBox categorySelectionCheckbox;

  @NonNull
  public final ImageView categorySelectionIcon;

  @NonNull
  public final TextView categorySelectionName;

  private ItemBudgetCategorySelectionBinding(@NonNull ConstraintLayout rootView,
      @NonNull CheckBox categorySelectionCheckbox, @NonNull ImageView categorySelectionIcon,
      @NonNull TextView categorySelectionName) {
    this.rootView = rootView;
    this.categorySelectionCheckbox = categorySelectionCheckbox;
    this.categorySelectionIcon = categorySelectionIcon;
    this.categorySelectionName = categorySelectionName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBudgetCategorySelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBudgetCategorySelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_budget_category_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBudgetCategorySelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.category_selection_checkbox;
      CheckBox categorySelectionCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (categorySelectionCheckbox == null) {
        break missingId;
      }

      id = R.id.category_selection_icon;
      ImageView categorySelectionIcon = ViewBindings.findChildViewById(rootView, id);
      if (categorySelectionIcon == null) {
        break missingId;
      }

      id = R.id.category_selection_name;
      TextView categorySelectionName = ViewBindings.findChildViewById(rootView, id);
      if (categorySelectionName == null) {
        break missingId;
      }

      return new ItemBudgetCategorySelectionBinding((ConstraintLayout) rootView,
          categorySelectionCheckbox, categorySelectionIcon, categorySelectionName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
