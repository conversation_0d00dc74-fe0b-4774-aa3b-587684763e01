// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogRecordingSubcategoryListViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final ImageView ivRecordingSubcategoryClose;

  @NonNull
  public final RecyclerView rvRecordingSubcategoryList;

  @NonNull
  public final TextView tvRecordingSubcategoryTitle;

  private DialogRecordingSubcategoryListViewBinding(@NonNull LinearLayout rootView,
      @NonNull View dragHandle, @NonNull ImageView ivRecordingSubcategoryClose,
      @NonNull RecyclerView rvRecordingSubcategoryList,
      @NonNull TextView tvRecordingSubcategoryTitle) {
    this.rootView = rootView;
    this.dragHandle = dragHandle;
    this.ivRecordingSubcategoryClose = ivRecordingSubcategoryClose;
    this.rvRecordingSubcategoryList = rvRecordingSubcategoryList;
    this.tvRecordingSubcategoryTitle = tvRecordingSubcategoryTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogRecordingSubcategoryListViewBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogRecordingSubcategoryListViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_recording_subcategory_list_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogRecordingSubcategoryListViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.iv_recording_subcategory_close;
      ImageView ivRecordingSubcategoryClose = ViewBindings.findChildViewById(rootView, id);
      if (ivRecordingSubcategoryClose == null) {
        break missingId;
      }

      id = R.id.rv_recording_subcategory_list;
      RecyclerView rvRecordingSubcategoryList = ViewBindings.findChildViewById(rootView, id);
      if (rvRecordingSubcategoryList == null) {
        break missingId;
      }

      id = R.id.tv_recording_subcategory_title;
      TextView tvRecordingSubcategoryTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingSubcategoryTitle == null) {
        break missingId;
      }

      return new DialogRecordingSubcategoryListViewBinding((LinearLayout) rootView, dragHandle,
          ivRecordingSubcategoryClose, rvRecordingSubcategoryList, tvRecordingSubcategoryTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
