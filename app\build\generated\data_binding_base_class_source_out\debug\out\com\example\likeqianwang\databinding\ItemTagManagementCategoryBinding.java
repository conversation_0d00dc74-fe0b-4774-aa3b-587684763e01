// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.flexbox.FlexboxLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTagManagementCategoryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final FlexboxLayout flexboxTags;

  @NonNull
  public final TextView tvAddTagToCategory;

  @NonNull
  public final TextView tvCategoryName;

  private ItemTagManagementCategoryBinding(@NonNull LinearLayout rootView,
      @NonNull FlexboxLayout flexboxTags, @NonNull TextView tvAddTagToCategory,
      @NonNull TextView tvCategoryName) {
    this.rootView = rootView;
    this.flexboxTags = flexboxTags;
    this.tvAddTagToCategory = tvAddTagToCategory;
    this.tvCategoryName = tvCategoryName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTagManagementCategoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTagManagementCategoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_tag_management_category, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTagManagementCategoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.flexbox_tags;
      FlexboxLayout flexboxTags = ViewBindings.findChildViewById(rootView, id);
      if (flexboxTags == null) {
        break missingId;
      }

      id = R.id.tv_add_tag_to_category;
      TextView tvAddTagToCategory = ViewBindings.findChildViewById(rootView, id);
      if (tvAddTagToCategory == null) {
        break missingId;
      }

      id = R.id.tv_category_name;
      TextView tvCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (tvCategoryName == null) {
        break missingId;
      }

      return new ItemTagManagementCategoryBinding((LinearLayout) rootView, flexboxTags,
          tvAddTagToCategory, tvCategoryName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
