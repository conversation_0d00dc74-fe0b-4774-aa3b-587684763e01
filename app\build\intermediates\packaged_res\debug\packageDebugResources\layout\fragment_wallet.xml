<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.main_wallets.WalletsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/wallets_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="15dp"
            android:text="@string/title_钱包"
            android:textColor="@color/black"
            android:textSize="25sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/wallets_add_new_account_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:contentDescription="@string/receipt_desc_设置"
            android:padding="15dp"
            android:scaleType="centerCrop"
            android:src="@drawable/frag_wallets_add"
            app:tint="@color/HuaQing" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/wallets_asset_summary_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wallets_title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:padding="15dp"
            android:background="@drawable/widget_common_bg">

            <TextView
                android:id="@+id/tv_widget_asset_liability_netAsset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:text="@string/wallet_净资产"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_widget_asset_liability_netAsset_balance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_widget_asset_liability_netAsset"
                tools:text="￥2,000.00"
                android:textColor="@color/black"
                android:textSize="30sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_widget_asset_liability_totalAsset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_widget_asset_liability_netAsset_balance"
                android:text="@string/wallet_总资产"
                android:layout_marginTop="8dp"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_widget_asset_liability_totalAsset_balance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toRightOf="@id/tv_widget_asset_liability_totalAsset"
                app:layout_constraintTop_toBottomOf="@id/tv_widget_asset_liability_netAsset_balance"
                tools:text="￥5,000.00"
                android:layout_marginTop="8dp"
                android:layout_marginStart="5dp"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_widget_asset_liability_totalLiability"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toRightOf="@id/tv_widget_asset_liability_totalAsset_balance"
                app:layout_constraintTop_toBottomOf="@id/tv_widget_asset_liability_netAsset_balance"
                android:text="@string/wallet_总负债"
                android:layout_marginTop="8dp"
                android:layout_marginStart="30dp"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_widget_asset_liability_totalLiability_balance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toRightOf="@id/tv_widget_asset_liability_totalLiability"
                app:layout_constraintTop_toBottomOf="@id/tv_widget_asset_liability_netAsset_balance"
                tools:text="￥3,000.00"
                android:layout_marginTop="8dp"
                android:layout_marginStart="5dp"
                android:textSize="16sp" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <!-- 添加账户分类列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/wallets_account_category_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>