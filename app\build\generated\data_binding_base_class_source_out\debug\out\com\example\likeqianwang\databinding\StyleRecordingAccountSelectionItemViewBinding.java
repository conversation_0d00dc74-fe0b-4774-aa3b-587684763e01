// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleRecordingAccountSelectionItemViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ShapeableImageView ivRecordingPageAccountItemIcon;

  @NonNull
  public final TextView tvRecordingPageAccountBalance;

  @NonNull
  public final TextView tvRecordingPageAccountName;

  @NonNull
  public final TextView tvRecordingPageAccountType;

  private StyleRecordingAccountSelectionItemViewBinding(@NonNull LinearLayout rootView,
      @NonNull ShapeableImageView ivRecordingPageAccountItemIcon,
      @NonNull TextView tvRecordingPageAccountBalance, @NonNull TextView tvRecordingPageAccountName,
      @NonNull TextView tvRecordingPageAccountType) {
    this.rootView = rootView;
    this.ivRecordingPageAccountItemIcon = ivRecordingPageAccountItemIcon;
    this.tvRecordingPageAccountBalance = tvRecordingPageAccountBalance;
    this.tvRecordingPageAccountName = tvRecordingPageAccountName;
    this.tvRecordingPageAccountType = tvRecordingPageAccountType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleRecordingAccountSelectionItemViewBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleRecordingAccountSelectionItemViewBinding inflate(
      @NonNull LayoutInflater inflater, @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_recording_account_selection_item_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleRecordingAccountSelectionItemViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_recording_page_account_item_icon;
      ShapeableImageView ivRecordingPageAccountItemIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivRecordingPageAccountItemIcon == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_account_balance;
      TextView tvRecordingPageAccountBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageAccountBalance == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_account_name;
      TextView tvRecordingPageAccountName = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageAccountName == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_account_type;
      TextView tvRecordingPageAccountType = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageAccountType == null) {
        break missingId;
      }

      return new StyleRecordingAccountSelectionItemViewBinding((LinearLayout) rootView,
          ivRecordingPageAccountItemIcon, tvRecordingPageAccountBalance, tvRecordingPageAccountName,
          tvRecordingPageAccountType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
