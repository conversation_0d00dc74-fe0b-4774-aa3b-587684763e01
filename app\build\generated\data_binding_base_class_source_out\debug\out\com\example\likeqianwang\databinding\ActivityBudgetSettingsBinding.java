// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBudgetSettingsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView budgetAddCategoryBudget;

  @NonNull
  public final RecyclerView budgetCategoryList;

  @NonNull
  public final SwitchCompat budgetNotificationSwitch;

  @NonNull
  public final SwitchCompat budgetOverBudgetAlertSwitch;

  @NonNull
  public final RadioGroup budgetPeriodGroup;

  @NonNull
  public final RadioButton budgetPeriodMonthly;

  @NonNull
  public final RadioButton budgetPeriodWeekly;

  @NonNull
  public final RadioButton budgetPeriodYearly;

  @NonNull
  public final ImageView budgetSettingsBack;

  @NonNull
  public final TextView budgetSettingsSave;

  @NonNull
  public final TextView budgetSettingsTitle;

  @NonNull
  public final ConstraintLayout budgetSettingsToolbar;

  @NonNull
  public final TextView budgetTotalAlertPercentage;

  @NonNull
  public final SeekBar budgetTotalAlertThreshold;

  @NonNull
  public final EditText budgetTotalAmount;

  private ActivityBudgetSettingsBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView budgetAddCategoryBudget, @NonNull RecyclerView budgetCategoryList,
      @NonNull SwitchCompat budgetNotificationSwitch,
      @NonNull SwitchCompat budgetOverBudgetAlertSwitch, @NonNull RadioGroup budgetPeriodGroup,
      @NonNull RadioButton budgetPeriodMonthly, @NonNull RadioButton budgetPeriodWeekly,
      @NonNull RadioButton budgetPeriodYearly, @NonNull ImageView budgetSettingsBack,
      @NonNull TextView budgetSettingsSave, @NonNull TextView budgetSettingsTitle,
      @NonNull ConstraintLayout budgetSettingsToolbar, @NonNull TextView budgetTotalAlertPercentage,
      @NonNull SeekBar budgetTotalAlertThreshold, @NonNull EditText budgetTotalAmount) {
    this.rootView = rootView;
    this.budgetAddCategoryBudget = budgetAddCategoryBudget;
    this.budgetCategoryList = budgetCategoryList;
    this.budgetNotificationSwitch = budgetNotificationSwitch;
    this.budgetOverBudgetAlertSwitch = budgetOverBudgetAlertSwitch;
    this.budgetPeriodGroup = budgetPeriodGroup;
    this.budgetPeriodMonthly = budgetPeriodMonthly;
    this.budgetPeriodWeekly = budgetPeriodWeekly;
    this.budgetPeriodYearly = budgetPeriodYearly;
    this.budgetSettingsBack = budgetSettingsBack;
    this.budgetSettingsSave = budgetSettingsSave;
    this.budgetSettingsTitle = budgetSettingsTitle;
    this.budgetSettingsToolbar = budgetSettingsToolbar;
    this.budgetTotalAlertPercentage = budgetTotalAlertPercentage;
    this.budgetTotalAlertThreshold = budgetTotalAlertThreshold;
    this.budgetTotalAmount = budgetTotalAmount;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBudgetSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBudgetSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_budget_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBudgetSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.budget_add_category_budget;
      TextView budgetAddCategoryBudget = ViewBindings.findChildViewById(rootView, id);
      if (budgetAddCategoryBudget == null) {
        break missingId;
      }

      id = R.id.budget_category_list;
      RecyclerView budgetCategoryList = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategoryList == null) {
        break missingId;
      }

      id = R.id.budget_notification_switch;
      SwitchCompat budgetNotificationSwitch = ViewBindings.findChildViewById(rootView, id);
      if (budgetNotificationSwitch == null) {
        break missingId;
      }

      id = R.id.budget_over_budget_alert_switch;
      SwitchCompat budgetOverBudgetAlertSwitch = ViewBindings.findChildViewById(rootView, id);
      if (budgetOverBudgetAlertSwitch == null) {
        break missingId;
      }

      id = R.id.budget_period_group;
      RadioGroup budgetPeriodGroup = ViewBindings.findChildViewById(rootView, id);
      if (budgetPeriodGroup == null) {
        break missingId;
      }

      id = R.id.budget_period_monthly;
      RadioButton budgetPeriodMonthly = ViewBindings.findChildViewById(rootView, id);
      if (budgetPeriodMonthly == null) {
        break missingId;
      }

      id = R.id.budget_period_weekly;
      RadioButton budgetPeriodWeekly = ViewBindings.findChildViewById(rootView, id);
      if (budgetPeriodWeekly == null) {
        break missingId;
      }

      id = R.id.budget_period_yearly;
      RadioButton budgetPeriodYearly = ViewBindings.findChildViewById(rootView, id);
      if (budgetPeriodYearly == null) {
        break missingId;
      }

      id = R.id.budget_settings_back;
      ImageView budgetSettingsBack = ViewBindings.findChildViewById(rootView, id);
      if (budgetSettingsBack == null) {
        break missingId;
      }

      id = R.id.budget_settings_save;
      TextView budgetSettingsSave = ViewBindings.findChildViewById(rootView, id);
      if (budgetSettingsSave == null) {
        break missingId;
      }

      id = R.id.budget_settings_title;
      TextView budgetSettingsTitle = ViewBindings.findChildViewById(rootView, id);
      if (budgetSettingsTitle == null) {
        break missingId;
      }

      id = R.id.budget_settings_toolbar;
      ConstraintLayout budgetSettingsToolbar = ViewBindings.findChildViewById(rootView, id);
      if (budgetSettingsToolbar == null) {
        break missingId;
      }

      id = R.id.budget_total_alert_percentage;
      TextView budgetTotalAlertPercentage = ViewBindings.findChildViewById(rootView, id);
      if (budgetTotalAlertPercentage == null) {
        break missingId;
      }

      id = R.id.budget_total_alert_threshold;
      SeekBar budgetTotalAlertThreshold = ViewBindings.findChildViewById(rootView, id);
      if (budgetTotalAlertThreshold == null) {
        break missingId;
      }

      id = R.id.budget_total_amount;
      EditText budgetTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (budgetTotalAmount == null) {
        break missingId;
      }

      return new ActivityBudgetSettingsBinding((ConstraintLayout) rootView, budgetAddCategoryBudget,
          budgetCategoryList, budgetNotificationSwitch, budgetOverBudgetAlertSwitch,
          budgetPeriodGroup, budgetPeriodMonthly, budgetPeriodWeekly, budgetPeriodYearly,
          budgetSettingsBack, budgetSettingsSave, budgetSettingsTitle, budgetSettingsToolbar,
          budgetTotalAlertPercentage, budgetTotalAlertThreshold, budgetTotalAmount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
