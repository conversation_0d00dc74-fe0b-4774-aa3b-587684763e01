// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogRecordingAccountSelectionViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final TextView recordingPageAccountSelectionBack;

  @NonNull
  public final RecyclerView recordingPageAccountSelectionList;

  @NonNull
  public final TextView recordingPageAccountSelectionTitle;

  private DialogRecordingAccountSelectionViewBinding(@NonNull LinearLayout rootView,
      @NonNull View dragHandle, @NonNull TextView recordingPageAccountSelectionBack,
      @NonNull RecyclerView recordingPageAccountSelectionList,
      @NonNull TextView recordingPageAccountSelectionTitle) {
    this.rootView = rootView;
    this.dragHandle = dragHandle;
    this.recordingPageAccountSelectionBack = recordingPageAccountSelectionBack;
    this.recordingPageAccountSelectionList = recordingPageAccountSelectionList;
    this.recordingPageAccountSelectionTitle = recordingPageAccountSelectionTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogRecordingAccountSelectionViewBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogRecordingAccountSelectionViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_recording_account_selection_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogRecordingAccountSelectionViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.recording_page_account_selection_back;
      TextView recordingPageAccountSelectionBack = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageAccountSelectionBack == null) {
        break missingId;
      }

      id = R.id.recording_page_account_selection_list;
      RecyclerView recordingPageAccountSelectionList = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageAccountSelectionList == null) {
        break missingId;
      }

      id = R.id.recording_page_account_selection_title;
      TextView recordingPageAccountSelectionTitle = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageAccountSelectionTitle == null) {
        break missingId;
      }

      return new DialogRecordingAccountSelectionViewBinding((LinearLayout) rootView, dragHandle,
          recordingPageAccountSelectionBack, recordingPageAccountSelectionList,
          recordingPageAccountSelectionTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
