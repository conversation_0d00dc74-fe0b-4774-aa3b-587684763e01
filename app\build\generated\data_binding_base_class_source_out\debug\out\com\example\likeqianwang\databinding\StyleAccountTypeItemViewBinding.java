// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleAccountTypeItemViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ShapeableImageView walletsAccountTypeItemIcon;

  @NonNull
  public final TextView walletsAccountTypeItemName;

  private StyleAccountTypeItemViewBinding(@NonNull LinearLayout rootView,
      @NonNull ShapeableImageView walletsAccountTypeItemIcon,
      @NonNull TextView walletsAccountTypeItemName) {
    this.rootView = rootView;
    this.walletsAccountTypeItemIcon = walletsAccountTypeItemIcon;
    this.walletsAccountTypeItemName = walletsAccountTypeItemName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleAccountTypeItemViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleAccountTypeItemViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_account_type_item_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleAccountTypeItemViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.wallets_account_type_item_icon;
      ShapeableImageView walletsAccountTypeItemIcon = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountTypeItemIcon == null) {
        break missingId;
      }

      id = R.id.wallets_account_type_item_name;
      TextView walletsAccountTypeItemName = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountTypeItemName == null) {
        break missingId;
      }

      return new StyleAccountTypeItemViewBinding((LinearLayout) rootView,
          walletsAccountTypeItemIcon, walletsAccountTypeItemName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
