<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recording_transfer_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\item_recording_transfer_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_recording_transfer_view_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="144" endOffset="51"/></Target><Target id="@+id/recording_page_guideline_vertical_center" view="androidx.constraintlayout.widget.Guideline"><Expressions/><location startLine="7" startOffset="4" endLine="12" endOffset="50"/></Target><Target id="@+id/recording_page_transfer_from_account" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="15" startOffset="4" endLine="64" endOffset="55"/></Target><Target id="@+id/iv_recording_page_transfer_from_account_icon" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="29" startOffset="8" endLine="36" endOffset="38"/></Target><Target id="@+id/tv_recording_page_transfer_from_account_name" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="50" endOffset="60"/></Target><Target id="@+id/tv_recording_page_transfer_from_account_balance" view="TextView"><Expressions/><location startLine="52" startOffset="8" endLine="62" endOffset="38"/></Target><Target id="@+id/recording_page_transfer_to_account" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="67" startOffset="4" endLine="116" endOffset="55"/></Target><Target id="@+id/iv_recording_page_transfer_to_account_icon" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="81" startOffset="8" endLine="88" endOffset="38"/></Target><Target id="@+id/tv_recording_page_transfer_to_account_name" view="TextView"><Expressions/><location startLine="90" startOffset="8" endLine="102" endOffset="60"/></Target><Target id="@+id/tv_recording_page_transfer_to_account_balance" view="TextView"><Expressions/><location startLine="104" startOffset="8" endLine="114" endOffset="38"/></Target><Target id="@+id/btn_recording_page_transfer_swap_accounts" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="119" startOffset="4" endLine="142" endOffset="31"/></Target></Targets></Layout>