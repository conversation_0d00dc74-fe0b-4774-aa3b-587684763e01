<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_new_account_bank_selection" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_add_new_account_bank_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/wallets_add_new_account_bank_selection"><Targets><Target id="@+id/wallets_add_new_account_bank_selection" tag="layout/dialog_add_new_account_bank_selection_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="103" endOffset="14"/></Target><Target id="@+id/drag_handle" view="View"><Expressions/><location startLine="15" startOffset="4" endLine="21" endOffset="38"/></Target><Target id="@+id/wallets_account_back" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="38" endOffset="89"/></Target><Target id="@+id/wallets_account_bank_selection_title" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="51" endOffset="54"/></Target><Target id="@+id/wallets_account_search_bank" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="56" startOffset="4" endLine="92" endOffset="55"/></Target><Target id="@+id/et_wallets_account_bank_search_input" view="EditText"><Expressions/><location startLine="77" startOffset="12" endLine="89" endOffset="53"/></Target><Target id="@+id/rv_bank_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="95" startOffset="4" endLine="101" endOffset="64"/></Target></Targets></Layout>