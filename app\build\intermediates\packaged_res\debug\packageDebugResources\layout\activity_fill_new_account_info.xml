<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/wallets_fill_new_account_info"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/YinBai"
    android:padding="15dp"
    tools:context=".FillNewAccountInfoActivity">

    <!--标题栏-->
    <TextView
        android:id="@+id/tv_wallets_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/add_new_account_返回"
        android:textSize="16sp"
        app:drawableStartCompat="@drawable/icon_arrow_back"
        app:layout_constraintBottom_toBottomOf="@id/tv_wallets_fill_info_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_wallets_fill_info_title" />

    <TextView
        android:id="@+id/tv_wallets_fill_info_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:text="@string/add_new_account_添加账户"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_wallets_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/add_new_account_保存"
        android:textColor="@color/HuaQing"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/tv_wallets_fill_info_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_wallets_fill_info_title" />

    <!--填写信息区域-->
    <LinearLayout
        android:id="@+id/wallets_account_basic_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:background="@drawable/widget_common_bg"
        android:divider="@drawable/widget_divider"
        android:dividerPadding="15dp"
        android:orientation="vertical"
        android:padding="10dp"
        android:showDividers="middle"
        app:layout_constraintTop_toBottomOf="@id/tv_wallets_fill_info_title">

        <LinearLayout
            android:id="@+id/wallets_account_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="5dp"
            android:paddingBottom="10dp">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_wallets_account_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:padding="1dp"
                app:shapeAppearanceOverlay="@style/circleIconStyle"
                app:strokeColor="@color/YinBai"
                app:strokeWidth="2dp" />

            <View
                android:layout_width="15dp"
                android:layout_height="match_parent" />

            <TextView
                android:id="@+id/tv_wallets_account_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:textColor="@color/black"
                android:textSize="16sp"
                app:drawableEndCompat="@drawable/icon_arrow_right"
                app:drawableTint="@color/grey" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="15dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_账户名称"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_wallets_account_input_name"
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:gravity="center_vertical|end"
                android:hint="@string/add_new_account_hint_请输入账户名称"
                android:importantForAutofill="no"
                android:inputType="text"
                android:textColor="@color/black"
                android:textSize="15sp"
                tools:ignore="TouchTargetSizeCheck" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/wallets_account_input_balance_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="15dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_账户余额"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_wallets_account_input_balance"
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:gravity="center_vertical|end"
                android:hint="@string/add_new_account_hint_0.00"
                android:importantForAutofill="no"
                android:inputType="numberDecimal"
                android:textColor="@color/black"
                android:textSize="15sp"
                tools:ignore="TouchTargetSizeCheck" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="15dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_账户备注"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_wallets_account_input_remark"
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:gravity="center_vertical|end"
                android:hint="@string/add_new_account_hint_点击输入备注"
                android:importantForAutofill="no"
                android:inputType="text"
                android:textColor="@color/black"
                android:textSize="15sp"
                tools:ignore="TouchTargetSizeCheck" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/wallets_account_credit_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:background="@drawable/widget_common_bg"
        android:divider="@drawable/widget_divider"
        android:dividerPadding="15dp"
        android:orientation="vertical"
        android:padding="10dp"
        android:showDividers="middle"
        app:layout_constraintTop_toBottomOf="@id/wallets_account_basic_info">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="15dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_总额度"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_wallets_account_input_total_credit"
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:gravity="center_vertical|end"
                android:hint="@string/add_new_account_hint_0.00"
                android:importantForAutofill="no"
                android:inputType="numberDecimal"
                android:textColor="@color/black"
                android:textSize="15sp"
                tools:ignore="TouchTargetSizeCheck" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="15dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_当前欠款"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_wallets_account_input_current_credit"
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:gravity="center_vertical|end"
                android:hint="@string/add_new_account_hint_0.00"
                android:importantForAutofill="no"
                android:inputType="numberDecimal"
                android:textColor="@color/black"
                android:textSize="15sp"
                tools:ignore="TouchTargetSizeCheck" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="5dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_账单日"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/et_wallets_account_input_statement_date"
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1"
                android:gravity="center_vertical|end"
                android:text="@string/add_new_account_hint_请点击设置"
                android:textSize="15sp"
                app:drawableEndCompat="@drawable/icon_arrow_right"
                app:drawableTint="@color/grey" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="5dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_还款日"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/et_wallets_account_input_due_date"
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1"
                android:gravity="center_vertical|end"
                android:text="@string/add_new_account_hint_请点击设置"
                android:textSize="15sp"
                app:drawableEndCompat="@drawable/icon_arrow_right"
                app:drawableTint="@color/grey" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingEnd="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_出账日账单计入当期"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <View
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1" />

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/switch_wallets_account_due_date_in_current_period"
                style="@style/Widget.MaterialComponents.CompoundButton.Switch"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:checked="true"
                android:gravity="center"
                app:thumbTint="@color/switchmaterial_thumb_color"
                app:trackTint="@color/switchmaterial_track_color" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:background="@drawable/widget_common_bg"
        android:divider="@drawable/widget_divider"
        android:dividerPadding="15dp"
        android:orientation="vertical"
        android:padding="10dp"
        android:showDividers="middle"
        app:layout_constraintTop_toBottomOf="@id/wallets_account_credit_info">

        <LinearLayout
            android:id="@+id/wallets_account_currency_selection"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="5dp"
            android:paddingBottom="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_账户币种"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_wallets_account_currency"
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1"
                android:gravity="center_vertical|end"
                android:text="CNY"
                android:textColor="@color/black"
                android:textSize="15sp"
                app:drawableEndCompat="@drawable/icon_arrow_right"
                app:drawableTint="@color/grey" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingEnd="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:gravity="center_vertical"
                android:text="@string/add_new_account_计入总资产"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <View
                android:layout_width="0dp"
                android:layout_height="28dp"
                android:layout_weight="1" />

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/switch_wallets_account_includeInAsset"
                style="@style/Widget.MaterialComponents.CompoundButton.Switch"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:checked="true"
                android:gravity="center"
                app:thumbTint="@color/switchmaterial_thumb_color"
                app:trackTint="@color/switchmaterial_track_color" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>