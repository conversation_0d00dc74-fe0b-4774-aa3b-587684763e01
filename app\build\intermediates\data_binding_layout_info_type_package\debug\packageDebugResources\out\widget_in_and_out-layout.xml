<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="widget_in_and_out" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\widget_in_and_out.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/widget_in_and_out_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="14"/></Target><Target id="@+id/widget_in_out_总支出" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/widget_in_out_total_expense_amount" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="27" endOffset="34"/></Target><Target id="@+id/widget_in_out_总收入" view="TextView"><Expressions/><location startLine="34" startOffset="8" endLine="41" endOffset="37"/></Target><Target id="@+id/widget_in_out_total_income_amount" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="50" endOffset="37"/></Target><Target id="@+id/widget_in_out_结余" view="TextView"><Expressions/><location startLine="52" startOffset="8" endLine="59" endOffset="37"/></Target><Target id="@+id/widget_in_out_balance_amount" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="68" endOffset="37"/></Target></Targets></Layout>