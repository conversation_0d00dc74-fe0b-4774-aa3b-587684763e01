// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleTagManagementItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivDeleteTag;

  @NonNull
  public final TextView tvTagName;

  private StyleTagManagementItemBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView ivDeleteTag, @NonNull TextView tvTagName) {
    this.rootView = rootView;
    this.ivDeleteTag = ivDeleteTag;
    this.tvTagName = tvTagName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleTagManagementItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleTagManagementItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_tag_management_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleTagManagementItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_delete_tag;
      ImageView ivDeleteTag = ViewBindings.findChildViewById(rootView, id);
      if (ivDeleteTag == null) {
        break missingId;
      }

      id = R.id.tv_tag_name;
      TextView tvTagName = ViewBindings.findChildViewById(rootView, id);
      if (tvTagName == null) {
        break missingId;
      }

      return new StyleTagManagementItemBinding((LinearLayout) rootView, ivDeleteTag, tvTagName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
