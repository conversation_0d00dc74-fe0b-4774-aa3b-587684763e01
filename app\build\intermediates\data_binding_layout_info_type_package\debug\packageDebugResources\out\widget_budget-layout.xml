<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="widget_budget" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\widget_budget.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/widget_budget_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="126" endOffset="14"/></Target><Target id="@+id/widget_budget_剩余预算" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="17" endOffset="34"/></Target><Target id="@+id/widget_budget_剩余预算金额" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="27" endOffset="34"/></Target><Target id="@+id/widget_budget_已用预算" view="TextView"><Expressions/><location startLine="42" startOffset="12" endLine="49" endOffset="41"/></Target><Target id="@+id/widget_budget_使用百分比" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="58" endOffset="42"/></Target><Target id="@+id/widget_budget_进度条" view="ProgressBar"><Expressions/><location startLine="62" startOffset="8" endLine="70" endOffset="80"/></Target><Target id="@+id/widget_budget_已用金额" view="TextView"><Expressions/><location startLine="78" startOffset="12" endLine="85" endOffset="41"/></Target><Target id="@+id/widget_budget_总预算金额" view="TextView"><Expressions/><location startLine="87" startOffset="12" endLine="93" endOffset="41"/></Target><Target id="@+id/widget_budget_状态指示器" view="View"><Expressions/><location startLine="107" startOffset="8" endLine="113" endOffset="53"/></Target><Target id="@+id/widget_budget_状态文本" view="TextView"><Expressions/><location startLine="115" startOffset="8" endLine="122" endOffset="38"/></Target></Targets></Layout>