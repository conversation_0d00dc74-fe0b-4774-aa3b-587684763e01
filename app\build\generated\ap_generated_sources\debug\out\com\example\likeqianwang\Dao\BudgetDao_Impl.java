package com.example.likeqianwang.Dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.likeqianwang.Entity.Budget;
import com.example.likeqianwang.Utils.Converters;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class BudgetDao_Impl implements BudgetDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Budget> __insertionAdapterOfBudget;

  private final EntityDeletionOrUpdateAdapter<Budget> __deletionAdapterOfBudget;

  private final EntityDeletionOrUpdateAdapter<Budget> __updateAdapterOfBudget;

  private final SharedSQLiteStatement __preparedStmtOfDeleteBudgetsByPeriod;

  public BudgetDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfBudget = new EntityInsertionAdapter<Budget>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `budgets` (`budgetId`,`budget_type`,`category_id`,`subcategory_id`,`budget_amount`,`budget_period`,`budget_year`,`budget_month`,`budget_week`,`is_active`,`alert_threshold`,`currency_symbol`,`create_time`,`update_time`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Budget entity) {
        if (entity.getBudgetId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getBudgetId());
        }
        if (entity.getBudgetType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getBudgetType());
        }
        if (entity.getCategoryId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, entity.getCategoryId());
        }
        if (entity.getSubcategoryId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, entity.getSubcategoryId());
        }
        final String _tmp = Converters.bigDecimalToString(entity.getBudgetAmount());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getBudgetPeriod());
        }
        statement.bindLong(7, entity.getBudgetYear());
        statement.bindLong(8, entity.getBudgetMonth());
        statement.bindLong(9, entity.getBudgetWeek());
        final int _tmp_1 = entity.isActive() ? 1 : 0;
        statement.bindLong(10, _tmp_1);
        statement.bindDouble(11, entity.getAlertThreshold());
        if (entity.getCurrencySymbol() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getCurrencySymbol());
        }
        final Long _tmp_2 = Converters.dateToTimestamp(entity.getCreateTime());
        if (_tmp_2 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_2);
        }
        final Long _tmp_3 = Converters.dateToTimestamp(entity.getUpdateTime());
        if (_tmp_3 == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, _tmp_3);
        }
        if (entity.getRemark() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getRemark());
        }
      }
    };
    this.__deletionAdapterOfBudget = new EntityDeletionOrUpdateAdapter<Budget>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `budgets` WHERE `budgetId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Budget entity) {
        if (entity.getBudgetId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getBudgetId());
        }
      }
    };
    this.__updateAdapterOfBudget = new EntityDeletionOrUpdateAdapter<Budget>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `budgets` SET `budgetId` = ?,`budget_type` = ?,`category_id` = ?,`subcategory_id` = ?,`budget_amount` = ?,`budget_period` = ?,`budget_year` = ?,`budget_month` = ?,`budget_week` = ?,`is_active` = ?,`alert_threshold` = ?,`currency_symbol` = ?,`create_time` = ?,`update_time` = ?,`remark` = ? WHERE `budgetId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Budget entity) {
        if (entity.getBudgetId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getBudgetId());
        }
        if (entity.getBudgetType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getBudgetType());
        }
        if (entity.getCategoryId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, entity.getCategoryId());
        }
        if (entity.getSubcategoryId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, entity.getSubcategoryId());
        }
        final String _tmp = Converters.bigDecimalToString(entity.getBudgetAmount());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getBudgetPeriod());
        }
        statement.bindLong(7, entity.getBudgetYear());
        statement.bindLong(8, entity.getBudgetMonth());
        statement.bindLong(9, entity.getBudgetWeek());
        final int _tmp_1 = entity.isActive() ? 1 : 0;
        statement.bindLong(10, _tmp_1);
        statement.bindDouble(11, entity.getAlertThreshold());
        if (entity.getCurrencySymbol() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getCurrencySymbol());
        }
        final Long _tmp_2 = Converters.dateToTimestamp(entity.getCreateTime());
        if (_tmp_2 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_2);
        }
        final Long _tmp_3 = Converters.dateToTimestamp(entity.getUpdateTime());
        if (_tmp_3 == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, _tmp_3);
        }
        if (entity.getRemark() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getRemark());
        }
        if (entity.getBudgetId() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getBudgetId());
        }
      }
    };
    this.__preparedStmtOfDeleteBudgetsByPeriod = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM budgets WHERE budget_period = ? AND budget_year = ? AND budget_month = ?";
        return _query;
      }
    };
  }

  @Override
  public long insert(final Budget budget) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfBudget.insertAndReturnId(budget);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final Budget budget) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfBudget.handle(budget);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final Budget budget) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfBudget.handle(budget);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void deleteBudgetsByPeriod(final String period, final int year, final int month) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteBudgetsByPeriod.acquire();
    int _argIndex = 1;
    if (period == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _stmt.bindLong(_argIndex, year);
    _argIndex = 3;
    _stmt.bindLong(_argIndex, month);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteBudgetsByPeriod.release(_stmt);
    }
  }

  @Override
  public Budget getBudgetById(final String budgetId) {
    final String _sql = "SELECT * FROM budgets WHERE budgetId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (budgetId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, budgetId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
      final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
      final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
      final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
      final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
      final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
      final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
      final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
      final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
      final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
      final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
      final Budget _result;
      if (_cursor.moveToFirst()) {
        _result = new Budget();
        final String _tmpBudgetId;
        if (_cursor.isNull(_cursorIndexOfBudgetId)) {
          _tmpBudgetId = null;
        } else {
          _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
        }
        _result.setBudgetId(_tmpBudgetId);
        final String _tmpBudgetType;
        if (_cursor.isNull(_cursorIndexOfBudgetType)) {
          _tmpBudgetType = null;
        } else {
          _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
        }
        _result.setBudgetType(_tmpBudgetType);
        final Long _tmpCategoryId;
        if (_cursor.isNull(_cursorIndexOfCategoryId)) {
          _tmpCategoryId = null;
        } else {
          _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        }
        _result.setCategoryId(_tmpCategoryId);
        final Long _tmpSubcategoryId;
        if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
          _tmpSubcategoryId = null;
        } else {
          _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        }
        _result.setSubcategoryId(_tmpSubcategoryId);
        final BigDecimal _tmpBudgetAmount;
        final String _tmp;
        if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
        }
        _tmpBudgetAmount = Converters.fromString(_tmp);
        _result.setBudgetAmount(_tmpBudgetAmount);
        final String _tmpBudgetPeriod;
        if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
          _tmpBudgetPeriod = null;
        } else {
          _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
        }
        _result.setBudgetPeriod(_tmpBudgetPeriod);
        final int _tmpBudgetYear;
        _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
        _result.setBudgetYear(_tmpBudgetYear);
        final int _tmpBudgetMonth;
        _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
        _result.setBudgetMonth(_tmpBudgetMonth);
        final int _tmpBudgetWeek;
        _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
        _result.setBudgetWeek(_tmpBudgetWeek);
        final boolean _tmpIsActive;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp_1 != 0;
        _result.setActive(_tmpIsActive);
        final double _tmpAlertThreshold;
        _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
        _result.setAlertThreshold(_tmpAlertThreshold);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _result.setCurrencySymbol(_tmpCurrencySymbol);
        final Date _tmpCreateTime;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreateTime)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
        }
        _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
        _result.setCreateTime(_tmpCreateTime);
        final Date _tmpUpdateTime;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
        }
        _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
        _result.setUpdateTime(_tmpUpdateTime);
        final String _tmpRemark;
        if (_cursor.isNull(_cursorIndexOfRemark)) {
          _tmpRemark = null;
        } else {
          _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
        }
        _result.setRemark(_tmpRemark);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<Budget> getBudgetByIdLive(final String budgetId) {
    final String _sql = "SELECT * FROM budgets WHERE budgetId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (budgetId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, budgetId);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"budgets"}, false, new Callable<Budget>() {
      @Override
      @Nullable
      public Budget call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
          final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
          final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
          final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final Budget _result;
          if (_cursor.moveToFirst()) {
            _result = new Budget();
            final String _tmpBudgetId;
            if (_cursor.isNull(_cursorIndexOfBudgetId)) {
              _tmpBudgetId = null;
            } else {
              _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
            }
            _result.setBudgetId(_tmpBudgetId);
            final String _tmpBudgetType;
            if (_cursor.isNull(_cursorIndexOfBudgetType)) {
              _tmpBudgetType = null;
            } else {
              _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
            }
            _result.setBudgetType(_tmpBudgetType);
            final Long _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            }
            _result.setCategoryId(_tmpCategoryId);
            final Long _tmpSubcategoryId;
            if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
              _tmpSubcategoryId = null;
            } else {
              _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            }
            _result.setSubcategoryId(_tmpSubcategoryId);
            final BigDecimal _tmpBudgetAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
            }
            _tmpBudgetAmount = Converters.fromString(_tmp);
            _result.setBudgetAmount(_tmpBudgetAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            _result.setBudgetPeriod(_tmpBudgetPeriod);
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            _result.setBudgetYear(_tmpBudgetYear);
            final int _tmpBudgetMonth;
            _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            _result.setBudgetMonth(_tmpBudgetMonth);
            final int _tmpBudgetWeek;
            _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            _result.setBudgetWeek(_tmpBudgetWeek);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            _result.setActive(_tmpIsActive);
            final double _tmpAlertThreshold;
            _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
            _result.setAlertThreshold(_tmpAlertThreshold);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _result.setCurrencySymbol(_tmpCurrencySymbol);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _result.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _result.setUpdateTime(_tmpUpdateTime);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _result.setRemark(_tmpRemark);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Budget>> getBudgetsByPeriod(final String period, final int year,
      final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    return __db.getInvalidationTracker().createLiveData(new String[] {"budgets"}, false, new Callable<List<Budget>>() {
      @Override
      @Nullable
      public List<Budget> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
          final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
          final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
          final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final List<Budget> _result = new ArrayList<Budget>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Budget _item;
            _item = new Budget();
            final String _tmpBudgetId;
            if (_cursor.isNull(_cursorIndexOfBudgetId)) {
              _tmpBudgetId = null;
            } else {
              _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
            }
            _item.setBudgetId(_tmpBudgetId);
            final String _tmpBudgetType;
            if (_cursor.isNull(_cursorIndexOfBudgetType)) {
              _tmpBudgetType = null;
            } else {
              _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
            }
            _item.setBudgetType(_tmpBudgetType);
            final Long _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            }
            _item.setCategoryId(_tmpCategoryId);
            final Long _tmpSubcategoryId;
            if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
              _tmpSubcategoryId = null;
            } else {
              _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            }
            _item.setSubcategoryId(_tmpSubcategoryId);
            final BigDecimal _tmpBudgetAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
            }
            _tmpBudgetAmount = Converters.fromString(_tmp);
            _item.setBudgetAmount(_tmpBudgetAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            _item.setBudgetPeriod(_tmpBudgetPeriod);
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            _item.setBudgetYear(_tmpBudgetYear);
            final int _tmpBudgetMonth;
            _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            _item.setBudgetMonth(_tmpBudgetMonth);
            final int _tmpBudgetWeek;
            _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            _item.setBudgetWeek(_tmpBudgetWeek);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            _item.setActive(_tmpIsActive);
            final double _tmpAlertThreshold;
            _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
            _item.setAlertThreshold(_tmpAlertThreshold);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _item.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _item.setUpdateTime(_tmpUpdateTime);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _item.setRemark(_tmpRemark);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<Budget> getBudgetsByPeriodSync(final String period, final int year, final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
      final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
      final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
      final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
      final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
      final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
      final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
      final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
      final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
      final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
      final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
      final List<Budget> _result = new ArrayList<Budget>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Budget _item;
        _item = new Budget();
        final String _tmpBudgetId;
        if (_cursor.isNull(_cursorIndexOfBudgetId)) {
          _tmpBudgetId = null;
        } else {
          _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
        }
        _item.setBudgetId(_tmpBudgetId);
        final String _tmpBudgetType;
        if (_cursor.isNull(_cursorIndexOfBudgetType)) {
          _tmpBudgetType = null;
        } else {
          _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
        }
        _item.setBudgetType(_tmpBudgetType);
        final Long _tmpCategoryId;
        if (_cursor.isNull(_cursorIndexOfCategoryId)) {
          _tmpCategoryId = null;
        } else {
          _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        }
        _item.setCategoryId(_tmpCategoryId);
        final Long _tmpSubcategoryId;
        if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
          _tmpSubcategoryId = null;
        } else {
          _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        }
        _item.setSubcategoryId(_tmpSubcategoryId);
        final BigDecimal _tmpBudgetAmount;
        final String _tmp;
        if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
        }
        _tmpBudgetAmount = Converters.fromString(_tmp);
        _item.setBudgetAmount(_tmpBudgetAmount);
        final String _tmpBudgetPeriod;
        if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
          _tmpBudgetPeriod = null;
        } else {
          _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
        }
        _item.setBudgetPeriod(_tmpBudgetPeriod);
        final int _tmpBudgetYear;
        _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
        _item.setBudgetYear(_tmpBudgetYear);
        final int _tmpBudgetMonth;
        _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
        _item.setBudgetMonth(_tmpBudgetMonth);
        final int _tmpBudgetWeek;
        _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
        _item.setBudgetWeek(_tmpBudgetWeek);
        final boolean _tmpIsActive;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp_1 != 0;
        _item.setActive(_tmpIsActive);
        final double _tmpAlertThreshold;
        _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
        _item.setAlertThreshold(_tmpAlertThreshold);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _item.setCurrencySymbol(_tmpCurrencySymbol);
        final Date _tmpCreateTime;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreateTime)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
        }
        _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
        _item.setCreateTime(_tmpCreateTime);
        final Date _tmpUpdateTime;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
        }
        _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
        _item.setUpdateTime(_tmpUpdateTime);
        final String _tmpRemark;
        if (_cursor.isNull(_cursorIndexOfRemark)) {
          _tmpRemark = null;
        } else {
          _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
        }
        _item.setRemark(_tmpRemark);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<Budget> getTotalBudget(final String period, final int year, final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_type = 'TOTAL' AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    return __db.getInvalidationTracker().createLiveData(new String[] {"budgets"}, false, new Callable<Budget>() {
      @Override
      @Nullable
      public Budget call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
          final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
          final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
          final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final Budget _result;
          if (_cursor.moveToFirst()) {
            _result = new Budget();
            final String _tmpBudgetId;
            if (_cursor.isNull(_cursorIndexOfBudgetId)) {
              _tmpBudgetId = null;
            } else {
              _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
            }
            _result.setBudgetId(_tmpBudgetId);
            final String _tmpBudgetType;
            if (_cursor.isNull(_cursorIndexOfBudgetType)) {
              _tmpBudgetType = null;
            } else {
              _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
            }
            _result.setBudgetType(_tmpBudgetType);
            final Long _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            }
            _result.setCategoryId(_tmpCategoryId);
            final Long _tmpSubcategoryId;
            if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
              _tmpSubcategoryId = null;
            } else {
              _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            }
            _result.setSubcategoryId(_tmpSubcategoryId);
            final BigDecimal _tmpBudgetAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
            }
            _tmpBudgetAmount = Converters.fromString(_tmp);
            _result.setBudgetAmount(_tmpBudgetAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            _result.setBudgetPeriod(_tmpBudgetPeriod);
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            _result.setBudgetYear(_tmpBudgetYear);
            final int _tmpBudgetMonth;
            _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            _result.setBudgetMonth(_tmpBudgetMonth);
            final int _tmpBudgetWeek;
            _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            _result.setBudgetWeek(_tmpBudgetWeek);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            _result.setActive(_tmpIsActive);
            final double _tmpAlertThreshold;
            _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
            _result.setAlertThreshold(_tmpAlertThreshold);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _result.setCurrencySymbol(_tmpCurrencySymbol);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _result.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _result.setUpdateTime(_tmpUpdateTime);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _result.setRemark(_tmpRemark);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Budget getTotalBudgetSync(final String period, final int year, final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_type = 'TOTAL' AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
      final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
      final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
      final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
      final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
      final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
      final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
      final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
      final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
      final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
      final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
      final Budget _result;
      if (_cursor.moveToFirst()) {
        _result = new Budget();
        final String _tmpBudgetId;
        if (_cursor.isNull(_cursorIndexOfBudgetId)) {
          _tmpBudgetId = null;
        } else {
          _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
        }
        _result.setBudgetId(_tmpBudgetId);
        final String _tmpBudgetType;
        if (_cursor.isNull(_cursorIndexOfBudgetType)) {
          _tmpBudgetType = null;
        } else {
          _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
        }
        _result.setBudgetType(_tmpBudgetType);
        final Long _tmpCategoryId;
        if (_cursor.isNull(_cursorIndexOfCategoryId)) {
          _tmpCategoryId = null;
        } else {
          _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        }
        _result.setCategoryId(_tmpCategoryId);
        final Long _tmpSubcategoryId;
        if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
          _tmpSubcategoryId = null;
        } else {
          _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        }
        _result.setSubcategoryId(_tmpSubcategoryId);
        final BigDecimal _tmpBudgetAmount;
        final String _tmp;
        if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
        }
        _tmpBudgetAmount = Converters.fromString(_tmp);
        _result.setBudgetAmount(_tmpBudgetAmount);
        final String _tmpBudgetPeriod;
        if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
          _tmpBudgetPeriod = null;
        } else {
          _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
        }
        _result.setBudgetPeriod(_tmpBudgetPeriod);
        final int _tmpBudgetYear;
        _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
        _result.setBudgetYear(_tmpBudgetYear);
        final int _tmpBudgetMonth;
        _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
        _result.setBudgetMonth(_tmpBudgetMonth);
        final int _tmpBudgetWeek;
        _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
        _result.setBudgetWeek(_tmpBudgetWeek);
        final boolean _tmpIsActive;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp_1 != 0;
        _result.setActive(_tmpIsActive);
        final double _tmpAlertThreshold;
        _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
        _result.setAlertThreshold(_tmpAlertThreshold);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _result.setCurrencySymbol(_tmpCurrencySymbol);
        final Date _tmpCreateTime;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreateTime)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
        }
        _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
        _result.setCreateTime(_tmpCreateTime);
        final Date _tmpUpdateTime;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
        }
        _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
        _result.setUpdateTime(_tmpUpdateTime);
        final String _tmpRemark;
        if (_cursor.isNull(_cursorIndexOfRemark)) {
          _tmpRemark = null;
        } else {
          _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
        }
        _result.setRemark(_tmpRemark);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<Budget> getCategoryBudget(final long categoryId, final String period,
      final int year, final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_type = 'CATEGORY' AND category_id = ? AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, categoryId);
    _argIndex = 2;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 3;
    _statement.bindLong(_argIndex, year);
    _argIndex = 4;
    _statement.bindLong(_argIndex, month);
    return __db.getInvalidationTracker().createLiveData(new String[] {"budgets"}, false, new Callable<Budget>() {
      @Override
      @Nullable
      public Budget call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
          final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
          final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
          final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final Budget _result;
          if (_cursor.moveToFirst()) {
            _result = new Budget();
            final String _tmpBudgetId;
            if (_cursor.isNull(_cursorIndexOfBudgetId)) {
              _tmpBudgetId = null;
            } else {
              _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
            }
            _result.setBudgetId(_tmpBudgetId);
            final String _tmpBudgetType;
            if (_cursor.isNull(_cursorIndexOfBudgetType)) {
              _tmpBudgetType = null;
            } else {
              _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
            }
            _result.setBudgetType(_tmpBudgetType);
            final Long _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            }
            _result.setCategoryId(_tmpCategoryId);
            final Long _tmpSubcategoryId;
            if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
              _tmpSubcategoryId = null;
            } else {
              _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            }
            _result.setSubcategoryId(_tmpSubcategoryId);
            final BigDecimal _tmpBudgetAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
            }
            _tmpBudgetAmount = Converters.fromString(_tmp);
            _result.setBudgetAmount(_tmpBudgetAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            _result.setBudgetPeriod(_tmpBudgetPeriod);
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            _result.setBudgetYear(_tmpBudgetYear);
            final int _tmpBudgetMonth;
            _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            _result.setBudgetMonth(_tmpBudgetMonth);
            final int _tmpBudgetWeek;
            _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            _result.setBudgetWeek(_tmpBudgetWeek);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            _result.setActive(_tmpIsActive);
            final double _tmpAlertThreshold;
            _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
            _result.setAlertThreshold(_tmpAlertThreshold);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _result.setCurrencySymbol(_tmpCurrencySymbol);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _result.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _result.setUpdateTime(_tmpUpdateTime);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _result.setRemark(_tmpRemark);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Budget getCategoryBudgetSync(final long categoryId, final String period, final int year,
      final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_type = 'CATEGORY' AND category_id = ? AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, categoryId);
    _argIndex = 2;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 3;
    _statement.bindLong(_argIndex, year);
    _argIndex = 4;
    _statement.bindLong(_argIndex, month);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
      final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
      final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
      final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
      final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
      final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
      final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
      final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
      final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
      final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
      final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
      final Budget _result;
      if (_cursor.moveToFirst()) {
        _result = new Budget();
        final String _tmpBudgetId;
        if (_cursor.isNull(_cursorIndexOfBudgetId)) {
          _tmpBudgetId = null;
        } else {
          _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
        }
        _result.setBudgetId(_tmpBudgetId);
        final String _tmpBudgetType;
        if (_cursor.isNull(_cursorIndexOfBudgetType)) {
          _tmpBudgetType = null;
        } else {
          _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
        }
        _result.setBudgetType(_tmpBudgetType);
        final Long _tmpCategoryId;
        if (_cursor.isNull(_cursorIndexOfCategoryId)) {
          _tmpCategoryId = null;
        } else {
          _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        }
        _result.setCategoryId(_tmpCategoryId);
        final Long _tmpSubcategoryId;
        if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
          _tmpSubcategoryId = null;
        } else {
          _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        }
        _result.setSubcategoryId(_tmpSubcategoryId);
        final BigDecimal _tmpBudgetAmount;
        final String _tmp;
        if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
        }
        _tmpBudgetAmount = Converters.fromString(_tmp);
        _result.setBudgetAmount(_tmpBudgetAmount);
        final String _tmpBudgetPeriod;
        if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
          _tmpBudgetPeriod = null;
        } else {
          _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
        }
        _result.setBudgetPeriod(_tmpBudgetPeriod);
        final int _tmpBudgetYear;
        _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
        _result.setBudgetYear(_tmpBudgetYear);
        final int _tmpBudgetMonth;
        _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
        _result.setBudgetMonth(_tmpBudgetMonth);
        final int _tmpBudgetWeek;
        _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
        _result.setBudgetWeek(_tmpBudgetWeek);
        final boolean _tmpIsActive;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp_1 != 0;
        _result.setActive(_tmpIsActive);
        final double _tmpAlertThreshold;
        _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
        _result.setAlertThreshold(_tmpAlertThreshold);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _result.setCurrencySymbol(_tmpCurrencySymbol);
        final Date _tmpCreateTime;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreateTime)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
        }
        _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
        _result.setCreateTime(_tmpCreateTime);
        final Date _tmpUpdateTime;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
        }
        _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
        _result.setUpdateTime(_tmpUpdateTime);
        final String _tmpRemark;
        if (_cursor.isNull(_cursorIndexOfRemark)) {
          _tmpRemark = null;
        } else {
          _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
        }
        _result.setRemark(_tmpRemark);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<Budget>> getAllCategoryBudgets(final String period, final int year,
      final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_type = 'CATEGORY' AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    return __db.getInvalidationTracker().createLiveData(new String[] {"budgets"}, false, new Callable<List<Budget>>() {
      @Override
      @Nullable
      public List<Budget> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
          final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
          final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
          final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final List<Budget> _result = new ArrayList<Budget>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Budget _item;
            _item = new Budget();
            final String _tmpBudgetId;
            if (_cursor.isNull(_cursorIndexOfBudgetId)) {
              _tmpBudgetId = null;
            } else {
              _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
            }
            _item.setBudgetId(_tmpBudgetId);
            final String _tmpBudgetType;
            if (_cursor.isNull(_cursorIndexOfBudgetType)) {
              _tmpBudgetType = null;
            } else {
              _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
            }
            _item.setBudgetType(_tmpBudgetType);
            final Long _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            }
            _item.setCategoryId(_tmpCategoryId);
            final Long _tmpSubcategoryId;
            if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
              _tmpSubcategoryId = null;
            } else {
              _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            }
            _item.setSubcategoryId(_tmpSubcategoryId);
            final BigDecimal _tmpBudgetAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
            }
            _tmpBudgetAmount = Converters.fromString(_tmp);
            _item.setBudgetAmount(_tmpBudgetAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            _item.setBudgetPeriod(_tmpBudgetPeriod);
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            _item.setBudgetYear(_tmpBudgetYear);
            final int _tmpBudgetMonth;
            _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            _item.setBudgetMonth(_tmpBudgetMonth);
            final int _tmpBudgetWeek;
            _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            _item.setBudgetWeek(_tmpBudgetWeek);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            _item.setActive(_tmpIsActive);
            final double _tmpAlertThreshold;
            _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
            _item.setAlertThreshold(_tmpAlertThreshold);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _item.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _item.setUpdateTime(_tmpUpdateTime);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _item.setRemark(_tmpRemark);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<Budget> getAllCategoryBudgetsSync(final String period, final int year,
      final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_type = 'CATEGORY' AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
      final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
      final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
      final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
      final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
      final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
      final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
      final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
      final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
      final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
      final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
      final List<Budget> _result = new ArrayList<Budget>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Budget _item;
        _item = new Budget();
        final String _tmpBudgetId;
        if (_cursor.isNull(_cursorIndexOfBudgetId)) {
          _tmpBudgetId = null;
        } else {
          _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
        }
        _item.setBudgetId(_tmpBudgetId);
        final String _tmpBudgetType;
        if (_cursor.isNull(_cursorIndexOfBudgetType)) {
          _tmpBudgetType = null;
        } else {
          _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
        }
        _item.setBudgetType(_tmpBudgetType);
        final Long _tmpCategoryId;
        if (_cursor.isNull(_cursorIndexOfCategoryId)) {
          _tmpCategoryId = null;
        } else {
          _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        }
        _item.setCategoryId(_tmpCategoryId);
        final Long _tmpSubcategoryId;
        if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
          _tmpSubcategoryId = null;
        } else {
          _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        }
        _item.setSubcategoryId(_tmpSubcategoryId);
        final BigDecimal _tmpBudgetAmount;
        final String _tmp;
        if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
        }
        _tmpBudgetAmount = Converters.fromString(_tmp);
        _item.setBudgetAmount(_tmpBudgetAmount);
        final String _tmpBudgetPeriod;
        if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
          _tmpBudgetPeriod = null;
        } else {
          _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
        }
        _item.setBudgetPeriod(_tmpBudgetPeriod);
        final int _tmpBudgetYear;
        _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
        _item.setBudgetYear(_tmpBudgetYear);
        final int _tmpBudgetMonth;
        _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
        _item.setBudgetMonth(_tmpBudgetMonth);
        final int _tmpBudgetWeek;
        _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
        _item.setBudgetWeek(_tmpBudgetWeek);
        final boolean _tmpIsActive;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp_1 != 0;
        _item.setActive(_tmpIsActive);
        final double _tmpAlertThreshold;
        _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
        _item.setAlertThreshold(_tmpAlertThreshold);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _item.setCurrencySymbol(_tmpCurrencySymbol);
        final Date _tmpCreateTime;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreateTime)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
        }
        _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
        _item.setCreateTime(_tmpCreateTime);
        final Date _tmpUpdateTime;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
        }
        _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
        _item.setUpdateTime(_tmpUpdateTime);
        final String _tmpRemark;
        if (_cursor.isNull(_cursorIndexOfRemark)) {
          _tmpRemark = null;
        } else {
          _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
        }
        _item.setRemark(_tmpRemark);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<Budget> getSubcategoryBudget(final long subcategoryId, final String period,
      final int year, final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_type = 'SUBCATEGORY' AND subcategory_id = ? AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, subcategoryId);
    _argIndex = 2;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 3;
    _statement.bindLong(_argIndex, year);
    _argIndex = 4;
    _statement.bindLong(_argIndex, month);
    return __db.getInvalidationTracker().createLiveData(new String[] {"budgets"}, false, new Callable<Budget>() {
      @Override
      @Nullable
      public Budget call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
          final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
          final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
          final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final Budget _result;
          if (_cursor.moveToFirst()) {
            _result = new Budget();
            final String _tmpBudgetId;
            if (_cursor.isNull(_cursorIndexOfBudgetId)) {
              _tmpBudgetId = null;
            } else {
              _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
            }
            _result.setBudgetId(_tmpBudgetId);
            final String _tmpBudgetType;
            if (_cursor.isNull(_cursorIndexOfBudgetType)) {
              _tmpBudgetType = null;
            } else {
              _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
            }
            _result.setBudgetType(_tmpBudgetType);
            final Long _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            }
            _result.setCategoryId(_tmpCategoryId);
            final Long _tmpSubcategoryId;
            if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
              _tmpSubcategoryId = null;
            } else {
              _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            }
            _result.setSubcategoryId(_tmpSubcategoryId);
            final BigDecimal _tmpBudgetAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
            }
            _tmpBudgetAmount = Converters.fromString(_tmp);
            _result.setBudgetAmount(_tmpBudgetAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            _result.setBudgetPeriod(_tmpBudgetPeriod);
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            _result.setBudgetYear(_tmpBudgetYear);
            final int _tmpBudgetMonth;
            _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            _result.setBudgetMonth(_tmpBudgetMonth);
            final int _tmpBudgetWeek;
            _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            _result.setBudgetWeek(_tmpBudgetWeek);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            _result.setActive(_tmpIsActive);
            final double _tmpAlertThreshold;
            _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
            _result.setAlertThreshold(_tmpAlertThreshold);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _result.setCurrencySymbol(_tmpCurrencySymbol);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _result.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _result.setUpdateTime(_tmpUpdateTime);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _result.setRemark(_tmpRemark);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Budget getSubcategoryBudgetSync(final long subcategoryId, final String period,
      final int year, final int month) {
    final String _sql = "SELECT * FROM budgets WHERE budget_type = 'SUBCATEGORY' AND subcategory_id = ? AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, subcategoryId);
    _argIndex = 2;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 3;
    _statement.bindLong(_argIndex, year);
    _argIndex = 4;
    _statement.bindLong(_argIndex, month);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
      final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
      final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
      final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
      final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
      final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
      final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
      final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
      final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
      final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
      final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
      final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
      final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
      final Budget _result;
      if (_cursor.moveToFirst()) {
        _result = new Budget();
        final String _tmpBudgetId;
        if (_cursor.isNull(_cursorIndexOfBudgetId)) {
          _tmpBudgetId = null;
        } else {
          _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
        }
        _result.setBudgetId(_tmpBudgetId);
        final String _tmpBudgetType;
        if (_cursor.isNull(_cursorIndexOfBudgetType)) {
          _tmpBudgetType = null;
        } else {
          _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
        }
        _result.setBudgetType(_tmpBudgetType);
        final Long _tmpCategoryId;
        if (_cursor.isNull(_cursorIndexOfCategoryId)) {
          _tmpCategoryId = null;
        } else {
          _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        }
        _result.setCategoryId(_tmpCategoryId);
        final Long _tmpSubcategoryId;
        if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
          _tmpSubcategoryId = null;
        } else {
          _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        }
        _result.setSubcategoryId(_tmpSubcategoryId);
        final BigDecimal _tmpBudgetAmount;
        final String _tmp;
        if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
        }
        _tmpBudgetAmount = Converters.fromString(_tmp);
        _result.setBudgetAmount(_tmpBudgetAmount);
        final String _tmpBudgetPeriod;
        if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
          _tmpBudgetPeriod = null;
        } else {
          _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
        }
        _result.setBudgetPeriod(_tmpBudgetPeriod);
        final int _tmpBudgetYear;
        _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
        _result.setBudgetYear(_tmpBudgetYear);
        final int _tmpBudgetMonth;
        _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
        _result.setBudgetMonth(_tmpBudgetMonth);
        final int _tmpBudgetWeek;
        _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
        _result.setBudgetWeek(_tmpBudgetWeek);
        final boolean _tmpIsActive;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
        _tmpIsActive = _tmp_1 != 0;
        _result.setActive(_tmpIsActive);
        final double _tmpAlertThreshold;
        _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
        _result.setAlertThreshold(_tmpAlertThreshold);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _result.setCurrencySymbol(_tmpCurrencySymbol);
        final Date _tmpCreateTime;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreateTime)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
        }
        _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
        _result.setCreateTime(_tmpCreateTime);
        final Date _tmpUpdateTime;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
        }
        _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
        _result.setUpdateTime(_tmpUpdateTime);
        final String _tmpRemark;
        if (_cursor.isNull(_cursorIndexOfRemark)) {
          _tmpRemark = null;
        } else {
          _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
        }
        _result.setRemark(_tmpRemark);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<Budget>> getAllBudgets() {
    final String _sql = "SELECT * FROM budgets WHERE is_active = 1 ORDER BY budget_year DESC, budget_month DESC, budget_type ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"budgets"}, false, new Callable<List<Budget>>() {
      @Override
      @Nullable
      public List<Budget> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfBudgetId = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetId");
          final int _cursorIndexOfBudgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_type");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "category_id");
          final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategory_id");
          final int _cursorIndexOfBudgetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_amount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_period");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_year");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_month");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budget_week");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "is_active");
          final int _cursorIndexOfAlertThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "alert_threshold");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currency_symbol");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "create_time");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "update_time");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final List<Budget> _result = new ArrayList<Budget>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Budget _item;
            _item = new Budget();
            final String _tmpBudgetId;
            if (_cursor.isNull(_cursorIndexOfBudgetId)) {
              _tmpBudgetId = null;
            } else {
              _tmpBudgetId = _cursor.getString(_cursorIndexOfBudgetId);
            }
            _item.setBudgetId(_tmpBudgetId);
            final String _tmpBudgetType;
            if (_cursor.isNull(_cursorIndexOfBudgetType)) {
              _tmpBudgetType = null;
            } else {
              _tmpBudgetType = _cursor.getString(_cursorIndexOfBudgetType);
            }
            _item.setBudgetType(_tmpBudgetType);
            final Long _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            }
            _item.setCategoryId(_tmpCategoryId);
            final Long _tmpSubcategoryId;
            if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
              _tmpSubcategoryId = null;
            } else {
              _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            }
            _item.setSubcategoryId(_tmpSubcategoryId);
            final BigDecimal _tmpBudgetAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
            }
            _tmpBudgetAmount = Converters.fromString(_tmp);
            _item.setBudgetAmount(_tmpBudgetAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            _item.setBudgetPeriod(_tmpBudgetPeriod);
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            _item.setBudgetYear(_tmpBudgetYear);
            final int _tmpBudgetMonth;
            _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            _item.setBudgetMonth(_tmpBudgetMonth);
            final int _tmpBudgetWeek;
            _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            _item.setBudgetWeek(_tmpBudgetWeek);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            _item.setActive(_tmpIsActive);
            final double _tmpAlertThreshold;
            _tmpAlertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
            _item.setAlertThreshold(_tmpAlertThreshold);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _item.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _item.setUpdateTime(_tmpUpdateTime);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _item.setRemark(_tmpRemark);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public int checkBudgetExists(final String budgetType, final Long categoryId,
      final Long subcategoryId, final String period, final int year, final int month) {
    final String _sql = "SELECT COUNT(*) FROM budgets WHERE budget_type = ? AND (? IS NULL OR category_id = ?) AND (? IS NULL OR subcategory_id = ?) AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 8);
    int _argIndex = 1;
    if (budgetType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, budgetType);
    }
    _argIndex = 2;
    if (categoryId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, categoryId);
    }
    _argIndex = 3;
    if (categoryId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, categoryId);
    }
    _argIndex = 4;
    if (subcategoryId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, subcategoryId);
    }
    _argIndex = 5;
    if (subcategoryId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, subcategoryId);
    }
    _argIndex = 6;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 7;
    _statement.bindLong(_argIndex, year);
    _argIndex = 8;
    _statement.bindLong(_argIndex, month);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public BigDecimal getTotalCategoryBudgetAmount(final String period, final int year,
      final int month) {
    final String _sql = "SELECT SUM(budget_amount) FROM budgets WHERE budget_type = 'CATEGORY' AND budget_period = ? AND budget_year = ? AND budget_month = ? AND is_active = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final BigDecimal _result;
      if (_cursor.moveToFirst()) {
        final String _tmp;
        if (_cursor.isNull(0)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getString(0);
        }
        _result = Converters.fromString(_tmp);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<BudgetDao.BudgetUsage>> getBudgetUsageByPeriod(final String period,
      final int year, final int month) {
    final String _sql = "SELECT b.budgetId, b.budget_type as budgetType, b.category_id as categoryId, b.subcategory_id as subcategoryId, b.budget_amount as budgetAmount, COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) as usedAmount, CASE WHEN b.budget_amount > 0 THEN    COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) * 100.0 / b.budget_amount ELSE 0 END as usagePercentage, CASE WHEN COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) > b.budget_amount THEN 1 ELSE 0 END as isOverBudget, b.alert_threshold as alertThreshold FROM budgets b LEFT JOIN transactions t ON    (b.budget_type = 'TOTAL' OR     (b.budget_type = 'CATEGORY' AND t.categoryId = b.category_id) OR     (b.budget_type = 'SUBCATEGORY' AND t.categoryId = b.category_id))    AND strftime('%Y', t.transactionDate/1000, 'unixepoch') = CAST(b.budget_year AS TEXT)    AND strftime('%m', t.transactionDate/1000, 'unixepoch') = printf('%02d', b.budget_month) WHERE b.budget_period = ? AND b.budget_year = ? AND b.budget_month = ? AND b.is_active = 1 GROUP BY b.budgetId";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    return __db.getInvalidationTracker().createLiveData(new String[] {"budgets",
        "transactions"}, false, new Callable<List<BudgetDao.BudgetUsage>>() {
      @Override
      @Nullable
      public List<BudgetDao.BudgetUsage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfBudgetId = 0;
          final int _cursorIndexOfBudgetType = 1;
          final int _cursorIndexOfCategoryId = 2;
          final int _cursorIndexOfSubcategoryId = 3;
          final int _cursorIndexOfBudgetAmount = 4;
          final int _cursorIndexOfUsedAmount = 5;
          final int _cursorIndexOfUsagePercentage = 6;
          final int _cursorIndexOfIsOverBudget = 7;
          final int _cursorIndexOfAlertThreshold = 8;
          final List<BudgetDao.BudgetUsage> _result = new ArrayList<BudgetDao.BudgetUsage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetDao.BudgetUsage _item;
            _item = new BudgetDao.BudgetUsage();
            if (_cursor.isNull(_cursorIndexOfBudgetId)) {
              _item.budgetId = null;
            } else {
              _item.budgetId = _cursor.getString(_cursorIndexOfBudgetId);
            }
            if (_cursor.isNull(_cursorIndexOfBudgetType)) {
              _item.budgetType = null;
            } else {
              _item.budgetType = _cursor.getString(_cursorIndexOfBudgetType);
            }
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _item.categoryId = null;
            } else {
              _item.categoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            }
            if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
              _item.subcategoryId = null;
            } else {
              _item.subcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            }
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
            }
            _item.budgetAmount = Converters.fromString(_tmp);
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUsedAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfUsedAmount);
            }
            _item.usedAmount = Converters.fromString(_tmp_1);
            _item.usagePercentage = _cursor.getDouble(_cursorIndexOfUsagePercentage);
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsOverBudget);
            _item.isOverBudget = _tmp_2 != 0;
            _item.alertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<BudgetDao.BudgetUsage> getBudgetUsageByPeriodSync(final String period, final int year,
      final int month) {
    final String _sql = "SELECT b.budgetId, b.budget_type as budgetType, b.category_id as categoryId, b.subcategory_id as subcategoryId, b.budget_amount as budgetAmount, COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) as usedAmount, CASE WHEN b.budget_amount > 0 THEN    COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) * 100.0 / b.budget_amount ELSE 0 END as usagePercentage, CASE WHEN COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) > b.budget_amount THEN 1 ELSE 0 END as isOverBudget, b.alert_threshold as alertThreshold FROM budgets b LEFT JOIN transactions t ON    (b.budget_type = 'TOTAL' OR     (b.budget_type = 'CATEGORY' AND t.categoryId = b.category_id) OR     (b.budget_type = 'SUBCATEGORY' AND t.categoryId = b.category_id))    AND strftime('%Y', t.transactionDate/1000, 'unixepoch') = CAST(b.budget_year AS TEXT)    AND strftime('%m', t.transactionDate/1000, 'unixepoch') = printf('%02d', b.budget_month) WHERE b.budget_period = ? AND b.budget_year = ? AND b.budget_month = ? AND b.is_active = 1 GROUP BY b.budgetId";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfBudgetId = 0;
      final int _cursorIndexOfBudgetType = 1;
      final int _cursorIndexOfCategoryId = 2;
      final int _cursorIndexOfSubcategoryId = 3;
      final int _cursorIndexOfBudgetAmount = 4;
      final int _cursorIndexOfUsedAmount = 5;
      final int _cursorIndexOfUsagePercentage = 6;
      final int _cursorIndexOfIsOverBudget = 7;
      final int _cursorIndexOfAlertThreshold = 8;
      final List<BudgetDao.BudgetUsage> _result = new ArrayList<BudgetDao.BudgetUsage>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final BudgetDao.BudgetUsage _item;
        _item = new BudgetDao.BudgetUsage();
        if (_cursor.isNull(_cursorIndexOfBudgetId)) {
          _item.budgetId = null;
        } else {
          _item.budgetId = _cursor.getString(_cursorIndexOfBudgetId);
        }
        if (_cursor.isNull(_cursorIndexOfBudgetType)) {
          _item.budgetType = null;
        } else {
          _item.budgetType = _cursor.getString(_cursorIndexOfBudgetType);
        }
        if (_cursor.isNull(_cursorIndexOfCategoryId)) {
          _item.categoryId = null;
        } else {
          _item.categoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        }
        if (_cursor.isNull(_cursorIndexOfSubcategoryId)) {
          _item.subcategoryId = null;
        } else {
          _item.subcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        }
        final String _tmp;
        if (_cursor.isNull(_cursorIndexOfBudgetAmount)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getString(_cursorIndexOfBudgetAmount);
        }
        _item.budgetAmount = Converters.fromString(_tmp);
        final String _tmp_1;
        if (_cursor.isNull(_cursorIndexOfUsedAmount)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getString(_cursorIndexOfUsedAmount);
        }
        _item.usedAmount = Converters.fromString(_tmp_1);
        _item.usagePercentage = _cursor.getDouble(_cursorIndexOfUsagePercentage);
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsOverBudget);
        _item.isOverBudget = _tmp_2 != 0;
        _item.alertThreshold = _cursor.getDouble(_cursorIndexOfAlertThreshold);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
