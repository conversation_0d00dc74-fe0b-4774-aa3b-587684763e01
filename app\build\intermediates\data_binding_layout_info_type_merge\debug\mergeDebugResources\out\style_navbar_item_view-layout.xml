<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="style_navbar_item_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\style_navbar_item_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/style_navbar_item_view_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="19" endOffset="14"/></Target><Target id="@+id/nav_image" view="ImageView"><Expressions/><location startLine="7" startOffset="4" endLine="10" endOffset="46"/></Target><Target id="@+id/nav_text" view="TextView"><Expressions/><location startLine="12" startOffset="4" endLine="17" endOffset="33"/></Target></Targets></Layout>