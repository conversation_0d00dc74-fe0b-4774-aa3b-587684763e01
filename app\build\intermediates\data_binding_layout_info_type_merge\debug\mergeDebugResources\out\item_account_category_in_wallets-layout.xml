<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_account_category_in_wallets" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\item_account_category_in_wallets.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_account_category_in_wallets_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="58" endOffset="14"/></Target><Target id="@+id/account_category" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="42" endOffset="18"/></Target><Target id="@+id/tv_account_category_name" view="TextView"><Expressions/><location startLine="17" startOffset="8" endLine="26" endOffset="30"/></Target><Target id="@+id/tv_account_category_total" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="35" endOffset="36"/></Target><Target id="@+id/iv_account_expand_icon" view="ImageView"><Expressions/><location startLine="37" startOffset="8" endLine="40" endOffset="50"/></Target><Target id="@+id/rv_account_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="51" startOffset="8" endLine="54" endOffset="50"/></Target></Targets></Layout>