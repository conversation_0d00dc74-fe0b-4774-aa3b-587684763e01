<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="16dp">

    <!-- 分类标题 -->
    <TextView
        android:id="@+id/tv_category_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="交通"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="交通" />

    <!-- 标签网格 -->
    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/flexbox_tags"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:alignItems="flex_start"
        app:flexWrap="wrap"
        app:justifyContent="flex_start" />

</LinearLayout>