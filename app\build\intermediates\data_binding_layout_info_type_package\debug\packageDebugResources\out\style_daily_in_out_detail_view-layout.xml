<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="style_daily_in_out_detail_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\style_daily_in_out_detail_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/style_daily_in_out_detail_view_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="94" endOffset="51"/></Target><Target id="@+id/receipt_Daily_InOut_icon" view="ImageView"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="38"/></Target><Target id="@+id/receipt_Daily_InOut_kind" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="40" endOffset="31"/></Target><Target id="@+id/receipt_Daily_InOut_remark" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="49" endOffset="35"/></Target><Target id="@+id/receipt_Daily_InOut_taglist" view="LinearLayout"><Expressions/><location startLine="51" startOffset="8" endLine="57" endOffset="41"/></Target><Target id="@+id/receipt_Daily_InOut_AmountAndAccount" view="LinearLayout"><Expressions/><location startLine="61" startOffset="4" endLine="92" endOffset="18"/></Target><Target id="@+id/receipt_Daily_InOut_amount" view="TextView"><Expressions/><location startLine="70" startOffset="8" endLine="80" endOffset="35"/></Target><Target id="@+id/receipt_Daily_InOut_account" view="TextView"><Expressions/><location startLine="82" startOffset="8" endLine="90" endOffset="37"/></Target></Targets></Layout>