package com.example.likeqianwang.Repository;

import android.app.Application;

import androidx.lifecycle.LiveData;

import com.example.likeqianwang.Dao.TransactionCategoryDao;
import com.example.likeqianwang.Dao.TransactionSubcategoryDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class TransactionCategoryRepository {
    private final TransactionCategoryDao categoryDao;
    private final TransactionSubcategoryDao subcategoryDao;
    private final ExecutorService executorService;

    public TransactionCategoryRepository(Application application) {
        AppDatabase database = AppDatabase.getInstance(application);
        categoryDao = database.transactionCategoryDao();
        subcategoryDao = database.transactionSubcategoryDao();
        executorService = Executors.newSingleThreadExecutor();
    }

    // 获取指定类型的分类列表
    public LiveData<List<TransactionCategory>> getCategoriesByType(int type) {
        return categoryDao.getCategoriesByType(type);
    }

    // 获取指定分类的子分类列表
    public LiveData<List<TransactionSubcategory>> getSubcategoriesByParentId(long parentCategoryId) {
        return subcategoryDao.getSubcategoriesByParentId(parentCategoryId);
    }

    // 加载分类及其子分类
    public void loadCategoriesWithSubcategories(int type, OnCategoriesLoadedListener listener) {
        executorService.execute(() -> {
            List<TransactionCategory> categories = categoryDao.getCategoriesByTypeSync(type);

            // 加载每个分类的子分类
            for (TransactionCategory category : categories) {
                if (category.isHasSubCategories()) {
                    List<TransactionSubcategory> subcategories =
                            subcategoryDao.getSubcategoriesByParentIdSync(category.getCategoryId());
                    category.setSubcategories(subcategories);
                }
            }

            // 回调结果
            listener.onCategoriesLoaded(categories);
        });
    }

    // 回调接口
    public interface OnCategoriesLoadedListener {
        void onCategoriesLoaded(List<TransactionCategory> categories);
    }
}
