<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/recording_page_DateTimePickerView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_bottom_sheet_background"
    android:paddingStart="15dp"
    android:paddingTop="10dp"
    android:paddingEnd="15dp"
    android:paddingBottom="25dp">

    <!-- 标题栏 -->
    <View
        android:id="@+id/drag_handle"
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_marginBottom="16dp"
        android:background="#DDDDDD"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/DateTimePicker_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="5dp"
        android:background="?attr/selectableItemBackground"
        android:padding="10dp"
        android:text="@string/确定"
        android:textColor="@color/HuaQing"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/drag_handle"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/DateTimePicker_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:background="?attr/selectableItemBackground"
        android:padding="10dp"
        android:text="@string/取消"
        android:textColor="#666666"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/DateTimePicker_confirm"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/DateTimePicker_confirm" />

    <DatePicker
        android:id="@+id/recording_page_DatePicker"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:layout_marginTop="16dp"
        android:calendarViewShown="false"
        android:datePickerMode="spinner"
        android:theme="@style/DatePickerStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/DateTimePicker_confirm" />

    <TimePicker
        android:id="@+id/recording_page_TimePicker"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:calendarViewShown="false"
        android:theme="@style/TimePickerStyle"
        android:timePickerMode="spinner"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recording_page_DatePicker" />

</androidx.constraintlayout.widget.ConstraintLayout>