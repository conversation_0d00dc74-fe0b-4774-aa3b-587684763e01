<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_tag_edit_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_tag_edit_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_tag_edit_view_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="14"/></Target><Target id="@+id/tv_dialog_title" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="19" endOffset="52"/></Target><Target id="@+id/et_tag_name" view="EditText"><Expressions/><location startLine="30" startOffset="4" endLine="39" endOffset="32"/></Target><Target id="@+id/spinner_tag_category" view="Spinner"><Expressions/><location startLine="50" startOffset="4" endLine="55" endOffset="44"/></Target><Target id="@+id/ll_new_category" view="LinearLayout"><Expressions/><location startLine="58" startOffset="4" endLine="84" endOffset="18"/></Target><Target id="@+id/et_new_category" view="EditText"><Expressions/><location startLine="73" startOffset="8" endLine="82" endOffset="35"/></Target><Target id="@+id/rv_color_picker" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="95" startOffset="4" endLine="100" endOffset="57"/></Target><Target id="@+id/btn_cancel" view="Button"><Expressions/><location startLine="108" startOffset="8" endLine="116" endOffset="44"/></Target><Target id="@+id/btn_save" view="Button"><Expressions/><location startLine="118" startOffset="8" endLine="126" endOffset="46"/></Target></Targets></Layout>