// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleAccountItemInWalletsBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout accountItemContent;

  @NonNull
  public final ShapeableImageView ivAccountItemIcon;

  @NonNull
  public final TextView tvAccountItemBalance;

  @NonNull
  public final TextView tvAccountItemCreditRemained;

  @NonNull
  public final TextView tvAccountItemDueTimeRemained;

  @NonNull
  public final TextView tvAccountItemIncludeInAsset;

  @NonNull
  public final TextView tvAccountItemName;

  @NonNull
  public final TextView tvAccountItemRemark;

  private StyleAccountItemInWalletsBinding(@NonNull MaterialCardView rootView,
      @NonNull LinearLayout accountItemContent, @NonNull ShapeableImageView ivAccountItemIcon,
      @NonNull TextView tvAccountItemBalance, @NonNull TextView tvAccountItemCreditRemained,
      @NonNull TextView tvAccountItemDueTimeRemained, @NonNull TextView tvAccountItemIncludeInAsset,
      @NonNull TextView tvAccountItemName, @NonNull TextView tvAccountItemRemark) {
    this.rootView = rootView;
    this.accountItemContent = accountItemContent;
    this.ivAccountItemIcon = ivAccountItemIcon;
    this.tvAccountItemBalance = tvAccountItemBalance;
    this.tvAccountItemCreditRemained = tvAccountItemCreditRemained;
    this.tvAccountItemDueTimeRemained = tvAccountItemDueTimeRemained;
    this.tvAccountItemIncludeInAsset = tvAccountItemIncludeInAsset;
    this.tvAccountItemName = tvAccountItemName;
    this.tvAccountItemRemark = tvAccountItemRemark;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleAccountItemInWalletsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleAccountItemInWalletsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_account_item_in_wallets, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleAccountItemInWalletsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.account_item_content;
      LinearLayout accountItemContent = ViewBindings.findChildViewById(rootView, id);
      if (accountItemContent == null) {
        break missingId;
      }

      id = R.id.iv_account_item_icon;
      ShapeableImageView ivAccountItemIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivAccountItemIcon == null) {
        break missingId;
      }

      id = R.id.tv_account_item_balance;
      TextView tvAccountItemBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountItemBalance == null) {
        break missingId;
      }

      id = R.id.tv_account_item_credit_remained;
      TextView tvAccountItemCreditRemained = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountItemCreditRemained == null) {
        break missingId;
      }

      id = R.id.tv_account_item_due_time_remained;
      TextView tvAccountItemDueTimeRemained = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountItemDueTimeRemained == null) {
        break missingId;
      }

      id = R.id.tv_account_item_includeInAsset;
      TextView tvAccountItemIncludeInAsset = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountItemIncludeInAsset == null) {
        break missingId;
      }

      id = R.id.tv_account_item_name;
      TextView tvAccountItemName = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountItemName == null) {
        break missingId;
      }

      id = R.id.tv_account_item_remark;
      TextView tvAccountItemRemark = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountItemRemark == null) {
        break missingId;
      }

      return new StyleAccountItemInWalletsBinding((MaterialCardView) rootView, accountItemContent,
          ivAccountItemIcon, tvAccountItemBalance, tvAccountItemCreditRemained,
          tvAccountItemDueTimeRemained, tvAccountItemIncludeInAsset, tvAccountItemName,
          tvAccountItemRemark);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
