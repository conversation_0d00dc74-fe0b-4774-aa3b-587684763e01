<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_tag_management_category" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\item_tag_management_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_tag_management_category_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="54" endOffset="14"/></Target><Target id="@+id/tv_category_name" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="29" endOffset="29"/></Target><Target id="@+id/tv_add_tag_to_category" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/flexbox_tags" view="com.google.android.flexbox.FlexboxLayout"><Expressions/><location startLine="46" startOffset="4" endLine="52" endOffset="41"/></Target></Targets></Layout>