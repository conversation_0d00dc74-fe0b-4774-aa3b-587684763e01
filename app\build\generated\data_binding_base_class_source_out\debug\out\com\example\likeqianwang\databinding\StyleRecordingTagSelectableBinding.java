// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class StyleRecordingTagSelectableBinding implements ViewBinding {
  @NonNull
  private final TextView rootView;

  @NonNull
  public final TextView tvTagName;

  private StyleRecordingTagSelectableBinding(@NonNull TextView rootView,
      @NonNull TextView tvTagName) {
    this.rootView = rootView;
    this.tvTagName = tvTagName;
  }

  @Override
  @NonNull
  public TextView getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleRecordingTagSelectableBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleRecordingTagSelectableBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_recording_tag_selectable, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleRecordingTagSelectableBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    TextView tvTagName = (TextView) rootView;

    return new StyleRecordingTagSelectableBinding((TextView) rootView, tvTagName);
  }
}
