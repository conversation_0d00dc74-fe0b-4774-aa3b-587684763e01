<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_account_type_category" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\item_account_type_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_account_type_category_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="31" endOffset="51"/></Target><Target id="@+id/wallets_account_type_category_name" view="TextView"><Expressions/><location startLine="6" startOffset="4" endLine="14" endOffset="51"/></Target><Target id="@+id/expand_icon" view="ImageView"><Expressions/><location startLine="16" startOffset="4" endLine="22" endOffset="56"/></Target><Target id="@+id/wallets_account_type_item_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="24" startOffset="4" endLine="29" endOffset="86"/></Target></Targets></Layout>