<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_wallet" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\fragment_wallet.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_wallet_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="126" endOffset="14"/></Target><Target id="@+id/wallets_title" view="TextView"><Expressions/><location startLine="14" startOffset="8" endLine="23" endOffset="38"/></Target><Target id="@+id/wallets_add_new_account_button" view="ImageView"><Expressions/><location startLine="25" startOffset="8" endLine="34" endOffset="39"/></Target><Target id="@+id/wallets_asset_summary_container" view="FrameLayout"><Expressions/><location startLine="38" startOffset="4" endLine="117" endOffset="17"/></Target><Target id="@+id/tv_widget_asset_liability_netAsset" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="60" endOffset="42"/></Target><Target id="@+id/tv_widget_asset_liability_netAsset_balance" view="TextView"><Expressions/><location startLine="62" startOffset="12" endLine="71" endOffset="42"/></Target><Target id="@+id/tv_widget_asset_liability_totalAsset" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="81" endOffset="41"/></Target><Target id="@+id/tv_widget_asset_liability_totalAsset_balance" view="TextView"><Expressions/><location startLine="83" startOffset="12" endLine="92" endOffset="41"/></Target><Target id="@+id/tv_widget_asset_liability_totalLiability" view="TextView"><Expressions/><location startLine="94" startOffset="12" endLine="103" endOffset="41"/></Target><Target id="@+id/tv_widget_asset_liability_totalLiability_balance" view="TextView"><Expressions/><location startLine="105" startOffset="12" endLine="114" endOffset="41"/></Target><Target id="@+id/wallets_account_category_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="120" startOffset="4" endLine="124" endOffset="35"/></Target></Targets></Layout>