[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_recording_subcategory_list_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_recording_subcategory_list_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_recording_tag_selection_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_recording_tag_selection_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_recording_tag_selectable.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_recording_tag_selectable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\activity_fill_new_account_info.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_fill_new_account_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_tag_color_picker.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_tag_color_picker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_date_time_picker_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_date_time_picker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_add_new_account_list_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_add_new_account_list_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\activity_tag_management.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_tag_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\activity_recording_page.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_recording_page.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\item_account_category_in_wallets.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_account_category_in_wallets.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_include_in_stats_budget_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_include_in_stats_budget_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\item_recording_transfer_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_recording_transfer_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_daily_in_out_detail_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_daily_in_out_detail_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_daily_in_out_detail_tag_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_daily_in_out_detail_tag_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_account_bank_item_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_account_bank_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_recording_account_selection_item_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_recording_account_selection_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\fragment_wallet.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\fragment_wallet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\item_tag_management_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_tag_management_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\widget_budget.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\widget_budget.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_tag_edit_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_tag_edit_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\item_budget_category_selection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_budget_category_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\item_account_type_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_account_type_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\fragment_receipt.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\fragment_receipt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\item_budget_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_budget_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_recording_account_selection_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_recording_account_selection_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_credit_date_picker_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_credit_date_picker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_recording_transfer_account_selection_item_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_recording_transfer_account_selection_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\activity_budget_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_budget_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_account_type_item_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_account_type_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_tag_management_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_tag_management_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_add_new_account_bank_selection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_add_new_account_bank_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_account_item_in_wallets.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_account_item_in_wallets.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\item_recording_tag_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_recording_tag_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\item_recording_in_out_category_list_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_recording_in_out_category_list_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_navbar_item_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_navbar_item_view.xml"}, {"merged": "com.example.likeqianwang.app-mergeDebugResources-49:/layout/widget_budget.xml", "source": "com.example.likeqianwang.app-main-52:/layout/widget_budget.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_recording_category_item_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_recording_category_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\fragment_stats.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\fragment_stats.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\style_daily_in_out_list_item_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_daily_in_out_list_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_budget_category_selection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_budget_category_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\widget_in_and_out.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\widget_in_and_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-mergeDebugResources-49:\\layout\\dialog_recording_transfer_account_selection_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_recording_transfer_account_selection_view.xml"}]