// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleTagColorPickerBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageView ivSelected;

  @NonNull
  public final View viewColor;

  private StyleTagColorPickerBinding(@NonNull FrameLayout rootView, @NonNull ImageView ivSelected,
      @NonNull View viewColor) {
    this.rootView = rootView;
    this.ivSelected = ivSelected;
    this.viewColor = viewColor;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleTagColorPickerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleTagColorPickerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_tag_color_picker, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleTagColorPickerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_selected;
      ImageView ivSelected = ViewBindings.findChildViewById(rootView, id);
      if (ivSelected == null) {
        break missingId;
      }

      id = R.id.view_color;
      View viewColor = ViewBindings.findChildViewById(rootView, id);
      if (viewColor == null) {
        break missingId;
      }

      return new StyleTagColorPickerBinding((FrameLayout) rootView, ivSelected, viewColor);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
