// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecordingInOutCategoryListViewBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final RecyclerView rvRecordingInOutCategoryList;

  private ItemRecordingInOutCategoryListViewBinding(@NonNull FrameLayout rootView,
      @NonNull RecyclerView rvRecordingInOutCategoryList) {
    this.rootView = rootView;
    this.rvRecordingInOutCategoryList = rvRecordingInOutCategoryList;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecordingInOutCategoryListViewBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecordingInOutCategoryListViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recording_in_out_category_list_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecordingInOutCategoryListViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.rv_recording_in_out_category_list;
      RecyclerView rvRecordingInOutCategoryList = ViewBindings.findChildViewById(rootView, id);
      if (rvRecordingInOutCategoryList == null) {
        break missingId;
      }

      return new ItemRecordingInOutCategoryListViewBinding((FrameLayout) rootView,
          rvRecordingInOutCategoryList);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
