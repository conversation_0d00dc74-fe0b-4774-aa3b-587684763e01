package com.example.likeqianwang.Dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Utils.Converters;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AccountDao_Impl implements AccountDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Account> __insertionAdapterOfAccount;

  private final EntityDeletionOrUpdateAdapter<Account> __deletionAdapterOfAccount;

  private final EntityDeletionOrUpdateAdapter<Account> __updateAdapterOfAccount;

  public AccountDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAccount = new EntityInsertionAdapter<Account>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `transaction_accounts` (`accountId`,`accountName`,`accountTypeId`,`accountTypeName`,`accountTypeIcon`,`accountTypeCategoryId`,`accountTypeDebitCredit`,`bankId`,`bankName`,`bankIcon`,`accountBalance`,`totalCredit`,`statementDate`,`dueDate`,`dueDateInCurrentPeriod`,`currencySymbol`,`includeInAsset`,`accountRemark`,`createTime`,`updateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Account entity) {
        if (entity.getAccountId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getAccountId());
        }
        if (entity.getAccountName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getAccountName());
        }
        if (entity.getAccountTypeId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getAccountTypeId());
        }
        if (entity.getAccountTypeName() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getAccountTypeName());
        }
        statement.bindLong(5, entity.getAccountTypeIcon());
        if (entity.getAccountTypeCategoryId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getAccountTypeCategoryId());
        }
        statement.bindLong(7, entity.getAccountTypeDebitCredit());
        if (entity.getBankId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getBankId());
        }
        if (entity.getBankName() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getBankName());
        }
        statement.bindLong(10, entity.getBankIcon());
        statement.bindDouble(11, entity.getAccountBalance());
        statement.bindDouble(12, entity.getTotalCredit());
        statement.bindLong(13, entity.getStatementDate());
        statement.bindLong(14, entity.getDueDate());
        final int _tmp = entity.isDueDateInCurrentPeriod() ? 1 : 0;
        statement.bindLong(15, _tmp);
        if (entity.getCurrencySymbol() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getCurrencySymbol());
        }
        final int _tmp_1 = entity.isIncludeInAsset() ? 1 : 0;
        statement.bindLong(17, _tmp_1);
        if (entity.getAccountRemark() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getAccountRemark());
        }
        final Long _tmp_2 = Converters.dateToTimestamp(entity.getCreateTime());
        if (_tmp_2 == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, _tmp_2);
        }
        final Long _tmp_3 = Converters.dateToTimestamp(entity.getUpdateTime());
        if (_tmp_3 == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, _tmp_3);
        }
      }
    };
    this.__deletionAdapterOfAccount = new EntityDeletionOrUpdateAdapter<Account>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `transaction_accounts` WHERE `accountId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Account entity) {
        if (entity.getAccountId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getAccountId());
        }
      }
    };
    this.__updateAdapterOfAccount = new EntityDeletionOrUpdateAdapter<Account>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `transaction_accounts` SET `accountId` = ?,`accountName` = ?,`accountTypeId` = ?,`accountTypeName` = ?,`accountTypeIcon` = ?,`accountTypeCategoryId` = ?,`accountTypeDebitCredit` = ?,`bankId` = ?,`bankName` = ?,`bankIcon` = ?,`accountBalance` = ?,`totalCredit` = ?,`statementDate` = ?,`dueDate` = ?,`dueDateInCurrentPeriod` = ?,`currencySymbol` = ?,`includeInAsset` = ?,`accountRemark` = ?,`createTime` = ?,`updateTime` = ? WHERE `accountId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement, final Account entity) {
        if (entity.getAccountId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getAccountId());
        }
        if (entity.getAccountName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getAccountName());
        }
        if (entity.getAccountTypeId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getAccountTypeId());
        }
        if (entity.getAccountTypeName() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getAccountTypeName());
        }
        statement.bindLong(5, entity.getAccountTypeIcon());
        if (entity.getAccountTypeCategoryId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getAccountTypeCategoryId());
        }
        statement.bindLong(7, entity.getAccountTypeDebitCredit());
        if (entity.getBankId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getBankId());
        }
        if (entity.getBankName() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getBankName());
        }
        statement.bindLong(10, entity.getBankIcon());
        statement.bindDouble(11, entity.getAccountBalance());
        statement.bindDouble(12, entity.getTotalCredit());
        statement.bindLong(13, entity.getStatementDate());
        statement.bindLong(14, entity.getDueDate());
        final int _tmp = entity.isDueDateInCurrentPeriod() ? 1 : 0;
        statement.bindLong(15, _tmp);
        if (entity.getCurrencySymbol() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getCurrencySymbol());
        }
        final int _tmp_1 = entity.isIncludeInAsset() ? 1 : 0;
        statement.bindLong(17, _tmp_1);
        if (entity.getAccountRemark() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getAccountRemark());
        }
        final Long _tmp_2 = Converters.dateToTimestamp(entity.getCreateTime());
        if (_tmp_2 == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, _tmp_2);
        }
        final Long _tmp_3 = Converters.dateToTimestamp(entity.getUpdateTime());
        if (_tmp_3 == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, _tmp_3);
        }
        if (entity.getAccountId() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getAccountId());
        }
      }
    };
  }

  @Override
  public void insert(final Account account) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __insertionAdapterOfAccount.insert(account);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final Account account) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfAccount.handle(account);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final Account account) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfAccount.handle(account);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public List<Account> getAllAccountsSync() {
    final String _sql = "SELECT * FROM transaction_accounts";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountId");
      final int _cursorIndexOfAccountName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountName");
      final int _cursorIndexOfAccountTypeId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeId");
      final int _cursorIndexOfAccountTypeName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeName");
      final int _cursorIndexOfAccountTypeIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeIcon");
      final int _cursorIndexOfAccountTypeCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeCategoryId");
      final int _cursorIndexOfAccountTypeDebitCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeDebitCredit");
      final int _cursorIndexOfBankId = CursorUtil.getColumnIndexOrThrow(_cursor, "bankId");
      final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
      final int _cursorIndexOfBankIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "bankIcon");
      final int _cursorIndexOfAccountBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "accountBalance");
      final int _cursorIndexOfTotalCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCredit");
      final int _cursorIndexOfStatementDate = CursorUtil.getColumnIndexOrThrow(_cursor, "statementDate");
      final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
      final int _cursorIndexOfDueDateInCurrentPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDateInCurrentPeriod");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
      final int _cursorIndexOfIncludeInAsset = CursorUtil.getColumnIndexOrThrow(_cursor, "includeInAsset");
      final int _cursorIndexOfAccountRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "accountRemark");
      final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
      final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
      final List<Account> _result = new ArrayList<Account>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final Account _item;
        _item = new Account();
        final String _tmpAccountId;
        if (_cursor.isNull(_cursorIndexOfAccountId)) {
          _tmpAccountId = null;
        } else {
          _tmpAccountId = _cursor.getString(_cursorIndexOfAccountId);
        }
        _item.setAccountId(_tmpAccountId);
        final String _tmpAccountName;
        if (_cursor.isNull(_cursorIndexOfAccountName)) {
          _tmpAccountName = null;
        } else {
          _tmpAccountName = _cursor.getString(_cursorIndexOfAccountName);
        }
        _item.setAccountName(_tmpAccountName);
        final String _tmpAccountTypeId;
        if (_cursor.isNull(_cursorIndexOfAccountTypeId)) {
          _tmpAccountTypeId = null;
        } else {
          _tmpAccountTypeId = _cursor.getString(_cursorIndexOfAccountTypeId);
        }
        _item.setAccountTypeId(_tmpAccountTypeId);
        final String _tmpAccountTypeName;
        if (_cursor.isNull(_cursorIndexOfAccountTypeName)) {
          _tmpAccountTypeName = null;
        } else {
          _tmpAccountTypeName = _cursor.getString(_cursorIndexOfAccountTypeName);
        }
        _item.setAccountTypeName(_tmpAccountTypeName);
        final int _tmpAccountTypeIcon;
        _tmpAccountTypeIcon = _cursor.getInt(_cursorIndexOfAccountTypeIcon);
        _item.setAccountTypeIcon(_tmpAccountTypeIcon);
        final String _tmpAccountTypeCategoryId;
        if (_cursor.isNull(_cursorIndexOfAccountTypeCategoryId)) {
          _tmpAccountTypeCategoryId = null;
        } else {
          _tmpAccountTypeCategoryId = _cursor.getString(_cursorIndexOfAccountTypeCategoryId);
        }
        _item.setAccountTypeCategoryId(_tmpAccountTypeCategoryId);
        final int _tmpAccountTypeDebitCredit;
        _tmpAccountTypeDebitCredit = _cursor.getInt(_cursorIndexOfAccountTypeDebitCredit);
        _item.setAccountTypeDebitCredit(_tmpAccountTypeDebitCredit);
        final String _tmpBankId;
        if (_cursor.isNull(_cursorIndexOfBankId)) {
          _tmpBankId = null;
        } else {
          _tmpBankId = _cursor.getString(_cursorIndexOfBankId);
        }
        _item.setBankId(_tmpBankId);
        final String _tmpBankName;
        if (_cursor.isNull(_cursorIndexOfBankName)) {
          _tmpBankName = null;
        } else {
          _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
        }
        _item.setBankName(_tmpBankName);
        final int _tmpBankIcon;
        _tmpBankIcon = _cursor.getInt(_cursorIndexOfBankIcon);
        _item.setBankIcon(_tmpBankIcon);
        final double _tmpAccountBalance;
        _tmpAccountBalance = _cursor.getDouble(_cursorIndexOfAccountBalance);
        _item.setAccountBalance(_tmpAccountBalance);
        final double _tmpTotalCredit;
        _tmpTotalCredit = _cursor.getDouble(_cursorIndexOfTotalCredit);
        _item.setTotalCredit(_tmpTotalCredit);
        final int _tmpStatementDate;
        _tmpStatementDate = _cursor.getInt(_cursorIndexOfStatementDate);
        _item.setStatementDate(_tmpStatementDate);
        final int _tmpDueDate;
        _tmpDueDate = _cursor.getInt(_cursorIndexOfDueDate);
        _item.setDueDate(_tmpDueDate);
        final boolean _tmpDueDateInCurrentPeriod;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfDueDateInCurrentPeriod);
        _tmpDueDateInCurrentPeriod = _tmp != 0;
        _item.setDueDateInCurrentPeriod(_tmpDueDateInCurrentPeriod);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _item.setCurrencySymbol(_tmpCurrencySymbol);
        final boolean _tmpIncludeInAsset;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIncludeInAsset);
        _tmpIncludeInAsset = _tmp_1 != 0;
        _item.setIncludeInAsset(_tmpIncludeInAsset);
        final String _tmpAccountRemark;
        if (_cursor.isNull(_cursorIndexOfAccountRemark)) {
          _tmpAccountRemark = null;
        } else {
          _tmpAccountRemark = _cursor.getString(_cursorIndexOfAccountRemark);
        }
        _item.setAccountRemark(_tmpAccountRemark);
        final Date _tmpCreateTime;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreateTime)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
        }
        _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
        _item.setCreateTime(_tmpCreateTime);
        final Date _tmpUpdateTime;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
        }
        _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
        _item.setUpdateTime(_tmpUpdateTime);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public Account getAccountByIdSync(final String accountId) {
    final String _sql = "SELECT * FROM transaction_accounts WHERE accountId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (accountId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, accountId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountId");
      final int _cursorIndexOfAccountName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountName");
      final int _cursorIndexOfAccountTypeId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeId");
      final int _cursorIndexOfAccountTypeName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeName");
      final int _cursorIndexOfAccountTypeIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeIcon");
      final int _cursorIndexOfAccountTypeCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeCategoryId");
      final int _cursorIndexOfAccountTypeDebitCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeDebitCredit");
      final int _cursorIndexOfBankId = CursorUtil.getColumnIndexOrThrow(_cursor, "bankId");
      final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
      final int _cursorIndexOfBankIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "bankIcon");
      final int _cursorIndexOfAccountBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "accountBalance");
      final int _cursorIndexOfTotalCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCredit");
      final int _cursorIndexOfStatementDate = CursorUtil.getColumnIndexOrThrow(_cursor, "statementDate");
      final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
      final int _cursorIndexOfDueDateInCurrentPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDateInCurrentPeriod");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
      final int _cursorIndexOfIncludeInAsset = CursorUtil.getColumnIndexOrThrow(_cursor, "includeInAsset");
      final int _cursorIndexOfAccountRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "accountRemark");
      final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
      final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
      final Account _result;
      if (_cursor.moveToFirst()) {
        _result = new Account();
        final String _tmpAccountId;
        if (_cursor.isNull(_cursorIndexOfAccountId)) {
          _tmpAccountId = null;
        } else {
          _tmpAccountId = _cursor.getString(_cursorIndexOfAccountId);
        }
        _result.setAccountId(_tmpAccountId);
        final String _tmpAccountName;
        if (_cursor.isNull(_cursorIndexOfAccountName)) {
          _tmpAccountName = null;
        } else {
          _tmpAccountName = _cursor.getString(_cursorIndexOfAccountName);
        }
        _result.setAccountName(_tmpAccountName);
        final String _tmpAccountTypeId;
        if (_cursor.isNull(_cursorIndexOfAccountTypeId)) {
          _tmpAccountTypeId = null;
        } else {
          _tmpAccountTypeId = _cursor.getString(_cursorIndexOfAccountTypeId);
        }
        _result.setAccountTypeId(_tmpAccountTypeId);
        final String _tmpAccountTypeName;
        if (_cursor.isNull(_cursorIndexOfAccountTypeName)) {
          _tmpAccountTypeName = null;
        } else {
          _tmpAccountTypeName = _cursor.getString(_cursorIndexOfAccountTypeName);
        }
        _result.setAccountTypeName(_tmpAccountTypeName);
        final int _tmpAccountTypeIcon;
        _tmpAccountTypeIcon = _cursor.getInt(_cursorIndexOfAccountTypeIcon);
        _result.setAccountTypeIcon(_tmpAccountTypeIcon);
        final String _tmpAccountTypeCategoryId;
        if (_cursor.isNull(_cursorIndexOfAccountTypeCategoryId)) {
          _tmpAccountTypeCategoryId = null;
        } else {
          _tmpAccountTypeCategoryId = _cursor.getString(_cursorIndexOfAccountTypeCategoryId);
        }
        _result.setAccountTypeCategoryId(_tmpAccountTypeCategoryId);
        final int _tmpAccountTypeDebitCredit;
        _tmpAccountTypeDebitCredit = _cursor.getInt(_cursorIndexOfAccountTypeDebitCredit);
        _result.setAccountTypeDebitCredit(_tmpAccountTypeDebitCredit);
        final String _tmpBankId;
        if (_cursor.isNull(_cursorIndexOfBankId)) {
          _tmpBankId = null;
        } else {
          _tmpBankId = _cursor.getString(_cursorIndexOfBankId);
        }
        _result.setBankId(_tmpBankId);
        final String _tmpBankName;
        if (_cursor.isNull(_cursorIndexOfBankName)) {
          _tmpBankName = null;
        } else {
          _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
        }
        _result.setBankName(_tmpBankName);
        final int _tmpBankIcon;
        _tmpBankIcon = _cursor.getInt(_cursorIndexOfBankIcon);
        _result.setBankIcon(_tmpBankIcon);
        final double _tmpAccountBalance;
        _tmpAccountBalance = _cursor.getDouble(_cursorIndexOfAccountBalance);
        _result.setAccountBalance(_tmpAccountBalance);
        final double _tmpTotalCredit;
        _tmpTotalCredit = _cursor.getDouble(_cursorIndexOfTotalCredit);
        _result.setTotalCredit(_tmpTotalCredit);
        final int _tmpStatementDate;
        _tmpStatementDate = _cursor.getInt(_cursorIndexOfStatementDate);
        _result.setStatementDate(_tmpStatementDate);
        final int _tmpDueDate;
        _tmpDueDate = _cursor.getInt(_cursorIndexOfDueDate);
        _result.setDueDate(_tmpDueDate);
        final boolean _tmpDueDateInCurrentPeriod;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfDueDateInCurrentPeriod);
        _tmpDueDateInCurrentPeriod = _tmp != 0;
        _result.setDueDateInCurrentPeriod(_tmpDueDateInCurrentPeriod);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _result.setCurrencySymbol(_tmpCurrencySymbol);
        final boolean _tmpIncludeInAsset;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIncludeInAsset);
        _tmpIncludeInAsset = _tmp_1 != 0;
        _result.setIncludeInAsset(_tmpIncludeInAsset);
        final String _tmpAccountRemark;
        if (_cursor.isNull(_cursorIndexOfAccountRemark)) {
          _tmpAccountRemark = null;
        } else {
          _tmpAccountRemark = _cursor.getString(_cursorIndexOfAccountRemark);
        }
        _result.setAccountRemark(_tmpAccountRemark);
        final Date _tmpCreateTime;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreateTime)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
        }
        _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
        _result.setCreateTime(_tmpCreateTime);
        final Date _tmpUpdateTime;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
        }
        _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
        _result.setUpdateTime(_tmpUpdateTime);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<Account> getAccountById(final String accountId) {
    final String _sql = "SELECT * FROM transaction_accounts WHERE accountId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (accountId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, accountId);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_accounts"}, false, new Callable<Account>() {
      @Override
      @Nullable
      public Account call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountId");
          final int _cursorIndexOfAccountName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountName");
          final int _cursorIndexOfAccountTypeId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeId");
          final int _cursorIndexOfAccountTypeName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeName");
          final int _cursorIndexOfAccountTypeIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeIcon");
          final int _cursorIndexOfAccountTypeCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeCategoryId");
          final int _cursorIndexOfAccountTypeDebitCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeDebitCredit");
          final int _cursorIndexOfBankId = CursorUtil.getColumnIndexOrThrow(_cursor, "bankId");
          final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
          final int _cursorIndexOfBankIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "bankIcon");
          final int _cursorIndexOfAccountBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "accountBalance");
          final int _cursorIndexOfTotalCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCredit");
          final int _cursorIndexOfStatementDate = CursorUtil.getColumnIndexOrThrow(_cursor, "statementDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfDueDateInCurrentPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDateInCurrentPeriod");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfIncludeInAsset = CursorUtil.getColumnIndexOrThrow(_cursor, "includeInAsset");
          final int _cursorIndexOfAccountRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "accountRemark");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final Account _result;
          if (_cursor.moveToFirst()) {
            _result = new Account();
            final String _tmpAccountId;
            if (_cursor.isNull(_cursorIndexOfAccountId)) {
              _tmpAccountId = null;
            } else {
              _tmpAccountId = _cursor.getString(_cursorIndexOfAccountId);
            }
            _result.setAccountId(_tmpAccountId);
            final String _tmpAccountName;
            if (_cursor.isNull(_cursorIndexOfAccountName)) {
              _tmpAccountName = null;
            } else {
              _tmpAccountName = _cursor.getString(_cursorIndexOfAccountName);
            }
            _result.setAccountName(_tmpAccountName);
            final String _tmpAccountTypeId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeId)) {
              _tmpAccountTypeId = null;
            } else {
              _tmpAccountTypeId = _cursor.getString(_cursorIndexOfAccountTypeId);
            }
            _result.setAccountTypeId(_tmpAccountTypeId);
            final String _tmpAccountTypeName;
            if (_cursor.isNull(_cursorIndexOfAccountTypeName)) {
              _tmpAccountTypeName = null;
            } else {
              _tmpAccountTypeName = _cursor.getString(_cursorIndexOfAccountTypeName);
            }
            _result.setAccountTypeName(_tmpAccountTypeName);
            final int _tmpAccountTypeIcon;
            _tmpAccountTypeIcon = _cursor.getInt(_cursorIndexOfAccountTypeIcon);
            _result.setAccountTypeIcon(_tmpAccountTypeIcon);
            final String _tmpAccountTypeCategoryId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeCategoryId)) {
              _tmpAccountTypeCategoryId = null;
            } else {
              _tmpAccountTypeCategoryId = _cursor.getString(_cursorIndexOfAccountTypeCategoryId);
            }
            _result.setAccountTypeCategoryId(_tmpAccountTypeCategoryId);
            final int _tmpAccountTypeDebitCredit;
            _tmpAccountTypeDebitCredit = _cursor.getInt(_cursorIndexOfAccountTypeDebitCredit);
            _result.setAccountTypeDebitCredit(_tmpAccountTypeDebitCredit);
            final String _tmpBankId;
            if (_cursor.isNull(_cursorIndexOfBankId)) {
              _tmpBankId = null;
            } else {
              _tmpBankId = _cursor.getString(_cursorIndexOfBankId);
            }
            _result.setBankId(_tmpBankId);
            final String _tmpBankName;
            if (_cursor.isNull(_cursorIndexOfBankName)) {
              _tmpBankName = null;
            } else {
              _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
            }
            _result.setBankName(_tmpBankName);
            final int _tmpBankIcon;
            _tmpBankIcon = _cursor.getInt(_cursorIndexOfBankIcon);
            _result.setBankIcon(_tmpBankIcon);
            final double _tmpAccountBalance;
            _tmpAccountBalance = _cursor.getDouble(_cursorIndexOfAccountBalance);
            _result.setAccountBalance(_tmpAccountBalance);
            final double _tmpTotalCredit;
            _tmpTotalCredit = _cursor.getDouble(_cursorIndexOfTotalCredit);
            _result.setTotalCredit(_tmpTotalCredit);
            final int _tmpStatementDate;
            _tmpStatementDate = _cursor.getInt(_cursorIndexOfStatementDate);
            _result.setStatementDate(_tmpStatementDate);
            final int _tmpDueDate;
            _tmpDueDate = _cursor.getInt(_cursorIndexOfDueDate);
            _result.setDueDate(_tmpDueDate);
            final boolean _tmpDueDateInCurrentPeriod;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfDueDateInCurrentPeriod);
            _tmpDueDateInCurrentPeriod = _tmp != 0;
            _result.setDueDateInCurrentPeriod(_tmpDueDateInCurrentPeriod);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _result.setCurrencySymbol(_tmpCurrencySymbol);
            final boolean _tmpIncludeInAsset;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIncludeInAsset);
            _tmpIncludeInAsset = _tmp_1 != 0;
            _result.setIncludeInAsset(_tmpIncludeInAsset);
            final String _tmpAccountRemark;
            if (_cursor.isNull(_cursorIndexOfAccountRemark)) {
              _tmpAccountRemark = null;
            } else {
              _tmpAccountRemark = _cursor.getString(_cursorIndexOfAccountRemark);
            }
            _result.setAccountRemark(_tmpAccountRemark);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _result.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _result.setUpdateTime(_tmpUpdateTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Account>> getAllAccounts() {
    final String _sql = "SELECT * FROM transaction_accounts ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_accounts"}, false, new Callable<List<Account>>() {
      @Override
      @Nullable
      public List<Account> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountId");
          final int _cursorIndexOfAccountName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountName");
          final int _cursorIndexOfAccountTypeId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeId");
          final int _cursorIndexOfAccountTypeName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeName");
          final int _cursorIndexOfAccountTypeIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeIcon");
          final int _cursorIndexOfAccountTypeCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeCategoryId");
          final int _cursorIndexOfAccountTypeDebitCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeDebitCredit");
          final int _cursorIndexOfBankId = CursorUtil.getColumnIndexOrThrow(_cursor, "bankId");
          final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
          final int _cursorIndexOfBankIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "bankIcon");
          final int _cursorIndexOfAccountBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "accountBalance");
          final int _cursorIndexOfTotalCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCredit");
          final int _cursorIndexOfStatementDate = CursorUtil.getColumnIndexOrThrow(_cursor, "statementDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfDueDateInCurrentPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDateInCurrentPeriod");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfIncludeInAsset = CursorUtil.getColumnIndexOrThrow(_cursor, "includeInAsset");
          final int _cursorIndexOfAccountRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "accountRemark");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<Account> _result = new ArrayList<Account>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Account _item;
            _item = new Account();
            final String _tmpAccountId;
            if (_cursor.isNull(_cursorIndexOfAccountId)) {
              _tmpAccountId = null;
            } else {
              _tmpAccountId = _cursor.getString(_cursorIndexOfAccountId);
            }
            _item.setAccountId(_tmpAccountId);
            final String _tmpAccountName;
            if (_cursor.isNull(_cursorIndexOfAccountName)) {
              _tmpAccountName = null;
            } else {
              _tmpAccountName = _cursor.getString(_cursorIndexOfAccountName);
            }
            _item.setAccountName(_tmpAccountName);
            final String _tmpAccountTypeId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeId)) {
              _tmpAccountTypeId = null;
            } else {
              _tmpAccountTypeId = _cursor.getString(_cursorIndexOfAccountTypeId);
            }
            _item.setAccountTypeId(_tmpAccountTypeId);
            final String _tmpAccountTypeName;
            if (_cursor.isNull(_cursorIndexOfAccountTypeName)) {
              _tmpAccountTypeName = null;
            } else {
              _tmpAccountTypeName = _cursor.getString(_cursorIndexOfAccountTypeName);
            }
            _item.setAccountTypeName(_tmpAccountTypeName);
            final int _tmpAccountTypeIcon;
            _tmpAccountTypeIcon = _cursor.getInt(_cursorIndexOfAccountTypeIcon);
            _item.setAccountTypeIcon(_tmpAccountTypeIcon);
            final String _tmpAccountTypeCategoryId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeCategoryId)) {
              _tmpAccountTypeCategoryId = null;
            } else {
              _tmpAccountTypeCategoryId = _cursor.getString(_cursorIndexOfAccountTypeCategoryId);
            }
            _item.setAccountTypeCategoryId(_tmpAccountTypeCategoryId);
            final int _tmpAccountTypeDebitCredit;
            _tmpAccountTypeDebitCredit = _cursor.getInt(_cursorIndexOfAccountTypeDebitCredit);
            _item.setAccountTypeDebitCredit(_tmpAccountTypeDebitCredit);
            final String _tmpBankId;
            if (_cursor.isNull(_cursorIndexOfBankId)) {
              _tmpBankId = null;
            } else {
              _tmpBankId = _cursor.getString(_cursorIndexOfBankId);
            }
            _item.setBankId(_tmpBankId);
            final String _tmpBankName;
            if (_cursor.isNull(_cursorIndexOfBankName)) {
              _tmpBankName = null;
            } else {
              _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
            }
            _item.setBankName(_tmpBankName);
            final int _tmpBankIcon;
            _tmpBankIcon = _cursor.getInt(_cursorIndexOfBankIcon);
            _item.setBankIcon(_tmpBankIcon);
            final double _tmpAccountBalance;
            _tmpAccountBalance = _cursor.getDouble(_cursorIndexOfAccountBalance);
            _item.setAccountBalance(_tmpAccountBalance);
            final double _tmpTotalCredit;
            _tmpTotalCredit = _cursor.getDouble(_cursorIndexOfTotalCredit);
            _item.setTotalCredit(_tmpTotalCredit);
            final int _tmpStatementDate;
            _tmpStatementDate = _cursor.getInt(_cursorIndexOfStatementDate);
            _item.setStatementDate(_tmpStatementDate);
            final int _tmpDueDate;
            _tmpDueDate = _cursor.getInt(_cursorIndexOfDueDate);
            _item.setDueDate(_tmpDueDate);
            final boolean _tmpDueDateInCurrentPeriod;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfDueDateInCurrentPeriod);
            _tmpDueDateInCurrentPeriod = _tmp != 0;
            _item.setDueDateInCurrentPeriod(_tmpDueDateInCurrentPeriod);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final boolean _tmpIncludeInAsset;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIncludeInAsset);
            _tmpIncludeInAsset = _tmp_1 != 0;
            _item.setIncludeInAsset(_tmpIncludeInAsset);
            final String _tmpAccountRemark;
            if (_cursor.isNull(_cursorIndexOfAccountRemark)) {
              _tmpAccountRemark = null;
            } else {
              _tmpAccountRemark = _cursor.getString(_cursorIndexOfAccountRemark);
            }
            _item.setAccountRemark(_tmpAccountRemark);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _item.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _item.setUpdateTime(_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Account>> getIncludedAccounts() {
    final String _sql = "SELECT * FROM transaction_accounts WHERE includeInAsset = 1 ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_accounts"}, false, new Callable<List<Account>>() {
      @Override
      @Nullable
      public List<Account> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountId");
          final int _cursorIndexOfAccountName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountName");
          final int _cursorIndexOfAccountTypeId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeId");
          final int _cursorIndexOfAccountTypeName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeName");
          final int _cursorIndexOfAccountTypeIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeIcon");
          final int _cursorIndexOfAccountTypeCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeCategoryId");
          final int _cursorIndexOfAccountTypeDebitCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeDebitCredit");
          final int _cursorIndexOfBankId = CursorUtil.getColumnIndexOrThrow(_cursor, "bankId");
          final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
          final int _cursorIndexOfBankIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "bankIcon");
          final int _cursorIndexOfAccountBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "accountBalance");
          final int _cursorIndexOfTotalCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCredit");
          final int _cursorIndexOfStatementDate = CursorUtil.getColumnIndexOrThrow(_cursor, "statementDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfDueDateInCurrentPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDateInCurrentPeriod");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfIncludeInAsset = CursorUtil.getColumnIndexOrThrow(_cursor, "includeInAsset");
          final int _cursorIndexOfAccountRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "accountRemark");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<Account> _result = new ArrayList<Account>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Account _item;
            _item = new Account();
            final String _tmpAccountId;
            if (_cursor.isNull(_cursorIndexOfAccountId)) {
              _tmpAccountId = null;
            } else {
              _tmpAccountId = _cursor.getString(_cursorIndexOfAccountId);
            }
            _item.setAccountId(_tmpAccountId);
            final String _tmpAccountName;
            if (_cursor.isNull(_cursorIndexOfAccountName)) {
              _tmpAccountName = null;
            } else {
              _tmpAccountName = _cursor.getString(_cursorIndexOfAccountName);
            }
            _item.setAccountName(_tmpAccountName);
            final String _tmpAccountTypeId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeId)) {
              _tmpAccountTypeId = null;
            } else {
              _tmpAccountTypeId = _cursor.getString(_cursorIndexOfAccountTypeId);
            }
            _item.setAccountTypeId(_tmpAccountTypeId);
            final String _tmpAccountTypeName;
            if (_cursor.isNull(_cursorIndexOfAccountTypeName)) {
              _tmpAccountTypeName = null;
            } else {
              _tmpAccountTypeName = _cursor.getString(_cursorIndexOfAccountTypeName);
            }
            _item.setAccountTypeName(_tmpAccountTypeName);
            final int _tmpAccountTypeIcon;
            _tmpAccountTypeIcon = _cursor.getInt(_cursorIndexOfAccountTypeIcon);
            _item.setAccountTypeIcon(_tmpAccountTypeIcon);
            final String _tmpAccountTypeCategoryId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeCategoryId)) {
              _tmpAccountTypeCategoryId = null;
            } else {
              _tmpAccountTypeCategoryId = _cursor.getString(_cursorIndexOfAccountTypeCategoryId);
            }
            _item.setAccountTypeCategoryId(_tmpAccountTypeCategoryId);
            final int _tmpAccountTypeDebitCredit;
            _tmpAccountTypeDebitCredit = _cursor.getInt(_cursorIndexOfAccountTypeDebitCredit);
            _item.setAccountTypeDebitCredit(_tmpAccountTypeDebitCredit);
            final String _tmpBankId;
            if (_cursor.isNull(_cursorIndexOfBankId)) {
              _tmpBankId = null;
            } else {
              _tmpBankId = _cursor.getString(_cursorIndexOfBankId);
            }
            _item.setBankId(_tmpBankId);
            final String _tmpBankName;
            if (_cursor.isNull(_cursorIndexOfBankName)) {
              _tmpBankName = null;
            } else {
              _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
            }
            _item.setBankName(_tmpBankName);
            final int _tmpBankIcon;
            _tmpBankIcon = _cursor.getInt(_cursorIndexOfBankIcon);
            _item.setBankIcon(_tmpBankIcon);
            final double _tmpAccountBalance;
            _tmpAccountBalance = _cursor.getDouble(_cursorIndexOfAccountBalance);
            _item.setAccountBalance(_tmpAccountBalance);
            final double _tmpTotalCredit;
            _tmpTotalCredit = _cursor.getDouble(_cursorIndexOfTotalCredit);
            _item.setTotalCredit(_tmpTotalCredit);
            final int _tmpStatementDate;
            _tmpStatementDate = _cursor.getInt(_cursorIndexOfStatementDate);
            _item.setStatementDate(_tmpStatementDate);
            final int _tmpDueDate;
            _tmpDueDate = _cursor.getInt(_cursorIndexOfDueDate);
            _item.setDueDate(_tmpDueDate);
            final boolean _tmpDueDateInCurrentPeriod;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfDueDateInCurrentPeriod);
            _tmpDueDateInCurrentPeriod = _tmp != 0;
            _item.setDueDateInCurrentPeriod(_tmpDueDateInCurrentPeriod);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final boolean _tmpIncludeInAsset;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIncludeInAsset);
            _tmpIncludeInAsset = _tmp_1 != 0;
            _item.setIncludeInAsset(_tmpIncludeInAsset);
            final String _tmpAccountRemark;
            if (_cursor.isNull(_cursorIndexOfAccountRemark)) {
              _tmpAccountRemark = null;
            } else {
              _tmpAccountRemark = _cursor.getString(_cursorIndexOfAccountRemark);
            }
            _item.setAccountRemark(_tmpAccountRemark);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _item.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _item.setUpdateTime(_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Account>> getAccountsByCategoryId(final String categoryId) {
    final String _sql = "SELECT * FROM transaction_accounts WHERE accountTypeCategoryId = ? ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (categoryId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, categoryId);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_accounts"}, false, new Callable<List<Account>>() {
      @Override
      @Nullable
      public List<Account> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountId");
          final int _cursorIndexOfAccountName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountName");
          final int _cursorIndexOfAccountTypeId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeId");
          final int _cursorIndexOfAccountTypeName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeName");
          final int _cursorIndexOfAccountTypeIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeIcon");
          final int _cursorIndexOfAccountTypeCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeCategoryId");
          final int _cursorIndexOfAccountTypeDebitCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeDebitCredit");
          final int _cursorIndexOfBankId = CursorUtil.getColumnIndexOrThrow(_cursor, "bankId");
          final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
          final int _cursorIndexOfBankIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "bankIcon");
          final int _cursorIndexOfAccountBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "accountBalance");
          final int _cursorIndexOfTotalCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCredit");
          final int _cursorIndexOfStatementDate = CursorUtil.getColumnIndexOrThrow(_cursor, "statementDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfDueDateInCurrentPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDateInCurrentPeriod");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfIncludeInAsset = CursorUtil.getColumnIndexOrThrow(_cursor, "includeInAsset");
          final int _cursorIndexOfAccountRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "accountRemark");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<Account> _result = new ArrayList<Account>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Account _item;
            _item = new Account();
            final String _tmpAccountId;
            if (_cursor.isNull(_cursorIndexOfAccountId)) {
              _tmpAccountId = null;
            } else {
              _tmpAccountId = _cursor.getString(_cursorIndexOfAccountId);
            }
            _item.setAccountId(_tmpAccountId);
            final String _tmpAccountName;
            if (_cursor.isNull(_cursorIndexOfAccountName)) {
              _tmpAccountName = null;
            } else {
              _tmpAccountName = _cursor.getString(_cursorIndexOfAccountName);
            }
            _item.setAccountName(_tmpAccountName);
            final String _tmpAccountTypeId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeId)) {
              _tmpAccountTypeId = null;
            } else {
              _tmpAccountTypeId = _cursor.getString(_cursorIndexOfAccountTypeId);
            }
            _item.setAccountTypeId(_tmpAccountTypeId);
            final String _tmpAccountTypeName;
            if (_cursor.isNull(_cursorIndexOfAccountTypeName)) {
              _tmpAccountTypeName = null;
            } else {
              _tmpAccountTypeName = _cursor.getString(_cursorIndexOfAccountTypeName);
            }
            _item.setAccountTypeName(_tmpAccountTypeName);
            final int _tmpAccountTypeIcon;
            _tmpAccountTypeIcon = _cursor.getInt(_cursorIndexOfAccountTypeIcon);
            _item.setAccountTypeIcon(_tmpAccountTypeIcon);
            final String _tmpAccountTypeCategoryId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeCategoryId)) {
              _tmpAccountTypeCategoryId = null;
            } else {
              _tmpAccountTypeCategoryId = _cursor.getString(_cursorIndexOfAccountTypeCategoryId);
            }
            _item.setAccountTypeCategoryId(_tmpAccountTypeCategoryId);
            final int _tmpAccountTypeDebitCredit;
            _tmpAccountTypeDebitCredit = _cursor.getInt(_cursorIndexOfAccountTypeDebitCredit);
            _item.setAccountTypeDebitCredit(_tmpAccountTypeDebitCredit);
            final String _tmpBankId;
            if (_cursor.isNull(_cursorIndexOfBankId)) {
              _tmpBankId = null;
            } else {
              _tmpBankId = _cursor.getString(_cursorIndexOfBankId);
            }
            _item.setBankId(_tmpBankId);
            final String _tmpBankName;
            if (_cursor.isNull(_cursorIndexOfBankName)) {
              _tmpBankName = null;
            } else {
              _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
            }
            _item.setBankName(_tmpBankName);
            final int _tmpBankIcon;
            _tmpBankIcon = _cursor.getInt(_cursorIndexOfBankIcon);
            _item.setBankIcon(_tmpBankIcon);
            final double _tmpAccountBalance;
            _tmpAccountBalance = _cursor.getDouble(_cursorIndexOfAccountBalance);
            _item.setAccountBalance(_tmpAccountBalance);
            final double _tmpTotalCredit;
            _tmpTotalCredit = _cursor.getDouble(_cursorIndexOfTotalCredit);
            _item.setTotalCredit(_tmpTotalCredit);
            final int _tmpStatementDate;
            _tmpStatementDate = _cursor.getInt(_cursorIndexOfStatementDate);
            _item.setStatementDate(_tmpStatementDate);
            final int _tmpDueDate;
            _tmpDueDate = _cursor.getInt(_cursorIndexOfDueDate);
            _item.setDueDate(_tmpDueDate);
            final boolean _tmpDueDateInCurrentPeriod;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfDueDateInCurrentPeriod);
            _tmpDueDateInCurrentPeriod = _tmp != 0;
            _item.setDueDateInCurrentPeriod(_tmpDueDateInCurrentPeriod);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final boolean _tmpIncludeInAsset;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIncludeInAsset);
            _tmpIncludeInAsset = _tmp_1 != 0;
            _item.setIncludeInAsset(_tmpIncludeInAsset);
            final String _tmpAccountRemark;
            if (_cursor.isNull(_cursorIndexOfAccountRemark)) {
              _tmpAccountRemark = null;
            } else {
              _tmpAccountRemark = _cursor.getString(_cursorIndexOfAccountRemark);
            }
            _item.setAccountRemark(_tmpAccountRemark);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _item.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _item.setUpdateTime(_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Account>> getDebitAccounts() {
    final String _sql = "SELECT * FROM transaction_accounts WHERE accountTypeDebitCredit = 1 ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_accounts"}, false, new Callable<List<Account>>() {
      @Override
      @Nullable
      public List<Account> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountId");
          final int _cursorIndexOfAccountName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountName");
          final int _cursorIndexOfAccountTypeId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeId");
          final int _cursorIndexOfAccountTypeName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeName");
          final int _cursorIndexOfAccountTypeIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeIcon");
          final int _cursorIndexOfAccountTypeCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeCategoryId");
          final int _cursorIndexOfAccountTypeDebitCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeDebitCredit");
          final int _cursorIndexOfBankId = CursorUtil.getColumnIndexOrThrow(_cursor, "bankId");
          final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
          final int _cursorIndexOfBankIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "bankIcon");
          final int _cursorIndexOfAccountBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "accountBalance");
          final int _cursorIndexOfTotalCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCredit");
          final int _cursorIndexOfStatementDate = CursorUtil.getColumnIndexOrThrow(_cursor, "statementDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfDueDateInCurrentPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDateInCurrentPeriod");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfIncludeInAsset = CursorUtil.getColumnIndexOrThrow(_cursor, "includeInAsset");
          final int _cursorIndexOfAccountRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "accountRemark");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<Account> _result = new ArrayList<Account>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Account _item;
            _item = new Account();
            final String _tmpAccountId;
            if (_cursor.isNull(_cursorIndexOfAccountId)) {
              _tmpAccountId = null;
            } else {
              _tmpAccountId = _cursor.getString(_cursorIndexOfAccountId);
            }
            _item.setAccountId(_tmpAccountId);
            final String _tmpAccountName;
            if (_cursor.isNull(_cursorIndexOfAccountName)) {
              _tmpAccountName = null;
            } else {
              _tmpAccountName = _cursor.getString(_cursorIndexOfAccountName);
            }
            _item.setAccountName(_tmpAccountName);
            final String _tmpAccountTypeId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeId)) {
              _tmpAccountTypeId = null;
            } else {
              _tmpAccountTypeId = _cursor.getString(_cursorIndexOfAccountTypeId);
            }
            _item.setAccountTypeId(_tmpAccountTypeId);
            final String _tmpAccountTypeName;
            if (_cursor.isNull(_cursorIndexOfAccountTypeName)) {
              _tmpAccountTypeName = null;
            } else {
              _tmpAccountTypeName = _cursor.getString(_cursorIndexOfAccountTypeName);
            }
            _item.setAccountTypeName(_tmpAccountTypeName);
            final int _tmpAccountTypeIcon;
            _tmpAccountTypeIcon = _cursor.getInt(_cursorIndexOfAccountTypeIcon);
            _item.setAccountTypeIcon(_tmpAccountTypeIcon);
            final String _tmpAccountTypeCategoryId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeCategoryId)) {
              _tmpAccountTypeCategoryId = null;
            } else {
              _tmpAccountTypeCategoryId = _cursor.getString(_cursorIndexOfAccountTypeCategoryId);
            }
            _item.setAccountTypeCategoryId(_tmpAccountTypeCategoryId);
            final int _tmpAccountTypeDebitCredit;
            _tmpAccountTypeDebitCredit = _cursor.getInt(_cursorIndexOfAccountTypeDebitCredit);
            _item.setAccountTypeDebitCredit(_tmpAccountTypeDebitCredit);
            final String _tmpBankId;
            if (_cursor.isNull(_cursorIndexOfBankId)) {
              _tmpBankId = null;
            } else {
              _tmpBankId = _cursor.getString(_cursorIndexOfBankId);
            }
            _item.setBankId(_tmpBankId);
            final String _tmpBankName;
            if (_cursor.isNull(_cursorIndexOfBankName)) {
              _tmpBankName = null;
            } else {
              _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
            }
            _item.setBankName(_tmpBankName);
            final int _tmpBankIcon;
            _tmpBankIcon = _cursor.getInt(_cursorIndexOfBankIcon);
            _item.setBankIcon(_tmpBankIcon);
            final double _tmpAccountBalance;
            _tmpAccountBalance = _cursor.getDouble(_cursorIndexOfAccountBalance);
            _item.setAccountBalance(_tmpAccountBalance);
            final double _tmpTotalCredit;
            _tmpTotalCredit = _cursor.getDouble(_cursorIndexOfTotalCredit);
            _item.setTotalCredit(_tmpTotalCredit);
            final int _tmpStatementDate;
            _tmpStatementDate = _cursor.getInt(_cursorIndexOfStatementDate);
            _item.setStatementDate(_tmpStatementDate);
            final int _tmpDueDate;
            _tmpDueDate = _cursor.getInt(_cursorIndexOfDueDate);
            _item.setDueDate(_tmpDueDate);
            final boolean _tmpDueDateInCurrentPeriod;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfDueDateInCurrentPeriod);
            _tmpDueDateInCurrentPeriod = _tmp != 0;
            _item.setDueDateInCurrentPeriod(_tmpDueDateInCurrentPeriod);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final boolean _tmpIncludeInAsset;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIncludeInAsset);
            _tmpIncludeInAsset = _tmp_1 != 0;
            _item.setIncludeInAsset(_tmpIncludeInAsset);
            final String _tmpAccountRemark;
            if (_cursor.isNull(_cursorIndexOfAccountRemark)) {
              _tmpAccountRemark = null;
            } else {
              _tmpAccountRemark = _cursor.getString(_cursorIndexOfAccountRemark);
            }
            _item.setAccountRemark(_tmpAccountRemark);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _item.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _item.setUpdateTime(_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Account>> getCreditAccounts() {
    final String _sql = "SELECT * FROM transaction_accounts WHERE accountTypeDebitCredit = 0 ORDER BY createTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_accounts"}, false, new Callable<List<Account>>() {
      @Override
      @Nullable
      public List<Account> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountId");
          final int _cursorIndexOfAccountName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountName");
          final int _cursorIndexOfAccountTypeId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeId");
          final int _cursorIndexOfAccountTypeName = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeName");
          final int _cursorIndexOfAccountTypeIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeIcon");
          final int _cursorIndexOfAccountTypeCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeCategoryId");
          final int _cursorIndexOfAccountTypeDebitCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "accountTypeDebitCredit");
          final int _cursorIndexOfBankId = CursorUtil.getColumnIndexOrThrow(_cursor, "bankId");
          final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
          final int _cursorIndexOfBankIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "bankIcon");
          final int _cursorIndexOfAccountBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "accountBalance");
          final int _cursorIndexOfTotalCredit = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCredit");
          final int _cursorIndexOfStatementDate = CursorUtil.getColumnIndexOrThrow(_cursor, "statementDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfDueDateInCurrentPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDateInCurrentPeriod");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfIncludeInAsset = CursorUtil.getColumnIndexOrThrow(_cursor, "includeInAsset");
          final int _cursorIndexOfAccountRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "accountRemark");
          final int _cursorIndexOfCreateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createTime");
          final int _cursorIndexOfUpdateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "updateTime");
          final List<Account> _result = new ArrayList<Account>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Account _item;
            _item = new Account();
            final String _tmpAccountId;
            if (_cursor.isNull(_cursorIndexOfAccountId)) {
              _tmpAccountId = null;
            } else {
              _tmpAccountId = _cursor.getString(_cursorIndexOfAccountId);
            }
            _item.setAccountId(_tmpAccountId);
            final String _tmpAccountName;
            if (_cursor.isNull(_cursorIndexOfAccountName)) {
              _tmpAccountName = null;
            } else {
              _tmpAccountName = _cursor.getString(_cursorIndexOfAccountName);
            }
            _item.setAccountName(_tmpAccountName);
            final String _tmpAccountTypeId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeId)) {
              _tmpAccountTypeId = null;
            } else {
              _tmpAccountTypeId = _cursor.getString(_cursorIndexOfAccountTypeId);
            }
            _item.setAccountTypeId(_tmpAccountTypeId);
            final String _tmpAccountTypeName;
            if (_cursor.isNull(_cursorIndexOfAccountTypeName)) {
              _tmpAccountTypeName = null;
            } else {
              _tmpAccountTypeName = _cursor.getString(_cursorIndexOfAccountTypeName);
            }
            _item.setAccountTypeName(_tmpAccountTypeName);
            final int _tmpAccountTypeIcon;
            _tmpAccountTypeIcon = _cursor.getInt(_cursorIndexOfAccountTypeIcon);
            _item.setAccountTypeIcon(_tmpAccountTypeIcon);
            final String _tmpAccountTypeCategoryId;
            if (_cursor.isNull(_cursorIndexOfAccountTypeCategoryId)) {
              _tmpAccountTypeCategoryId = null;
            } else {
              _tmpAccountTypeCategoryId = _cursor.getString(_cursorIndexOfAccountTypeCategoryId);
            }
            _item.setAccountTypeCategoryId(_tmpAccountTypeCategoryId);
            final int _tmpAccountTypeDebitCredit;
            _tmpAccountTypeDebitCredit = _cursor.getInt(_cursorIndexOfAccountTypeDebitCredit);
            _item.setAccountTypeDebitCredit(_tmpAccountTypeDebitCredit);
            final String _tmpBankId;
            if (_cursor.isNull(_cursorIndexOfBankId)) {
              _tmpBankId = null;
            } else {
              _tmpBankId = _cursor.getString(_cursorIndexOfBankId);
            }
            _item.setBankId(_tmpBankId);
            final String _tmpBankName;
            if (_cursor.isNull(_cursorIndexOfBankName)) {
              _tmpBankName = null;
            } else {
              _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
            }
            _item.setBankName(_tmpBankName);
            final int _tmpBankIcon;
            _tmpBankIcon = _cursor.getInt(_cursorIndexOfBankIcon);
            _item.setBankIcon(_tmpBankIcon);
            final double _tmpAccountBalance;
            _tmpAccountBalance = _cursor.getDouble(_cursorIndexOfAccountBalance);
            _item.setAccountBalance(_tmpAccountBalance);
            final double _tmpTotalCredit;
            _tmpTotalCredit = _cursor.getDouble(_cursorIndexOfTotalCredit);
            _item.setTotalCredit(_tmpTotalCredit);
            final int _tmpStatementDate;
            _tmpStatementDate = _cursor.getInt(_cursorIndexOfStatementDate);
            _item.setStatementDate(_tmpStatementDate);
            final int _tmpDueDate;
            _tmpDueDate = _cursor.getInt(_cursorIndexOfDueDate);
            _item.setDueDate(_tmpDueDate);
            final boolean _tmpDueDateInCurrentPeriod;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfDueDateInCurrentPeriod);
            _tmpDueDateInCurrentPeriod = _tmp != 0;
            _item.setDueDateInCurrentPeriod(_tmpDueDateInCurrentPeriod);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final boolean _tmpIncludeInAsset;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIncludeInAsset);
            _tmpIncludeInAsset = _tmp_1 != 0;
            _item.setIncludeInAsset(_tmpIncludeInAsset);
            final String _tmpAccountRemark;
            if (_cursor.isNull(_cursorIndexOfAccountRemark)) {
              _tmpAccountRemark = null;
            } else {
              _tmpAccountRemark = _cursor.getString(_cursorIndexOfAccountRemark);
            }
            _item.setAccountRemark(_tmpAccountRemark);
            final Date _tmpCreateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreateTime);
            }
            _tmpCreateTime = Converters.fromTimestampToDate(_tmp_2);
            _item.setCreateTime(_tmpCreateTime);
            final Date _tmpUpdateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdateTime);
            }
            _tmpUpdateTime = Converters.fromTimestampToDate(_tmp_3);
            _item.setUpdateTime(_tmpUpdateTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<String> getAllAccountTypeCategories() {
    final String _sql = "SELECT DISTINCT accountTypeCategoryId FROM transaction_accounts";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final List<String> _result = new ArrayList<String>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final String _item;
        if (_cursor.isNull(0)) {
          _item = null;
        } else {
          _item = _cursor.getString(0);
        }
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
