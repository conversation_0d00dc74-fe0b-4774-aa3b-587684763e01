<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="ChaHuaHong">#ee3f4d</color>
    <color name="HuaQing">#2376b7</color>
    <color name="QianH<PERSON>">#dad4cb</color>
    <color name="WaLv">#45b787</color>
    <color name="YinBai">#f1f0ed</color>
    <color name="black">#FF000000</color>
    <color name="defaultGrey">#8A000000</color>
    <color name="delete_red">#F44336</color>
    <color name="grey">#808080</color>
    <color name="light_grey">#d3d3d3</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="divider_height">1dp</dimen>
    <dimen name="divider_in_wallets_left_margin">15dp</dimen>
    <dimen name="divider_in_wallets_right_margin">15dp</dimen>
    <dimen name="divider_left_margin">25dp</dimen>
    <dimen name="divider_right_margin">25dp</dimen>
    <dimen name="recording_transfer_account_icon_circle_radius">20dp</dimen>
    <dimen name="recording_transfer_account_icon_padding">1dp</dimen>
    <dimen name="recording_transfer_account_icon_size">40dp</dimen>
    <dimen name="recording_transfer_account_name_padding_start">18dp</dimen>
    <string name="add_new_account_hint_0.00">0.00</string>
    <string name="add_new_account_hint_搜索">搜索</string>
    <string name="add_new_account_hint_点击输入备注">点击输入备注 (可不填)</string>
    <string name="add_new_account_hint_请点击设置">请点击设置</string>
    <string name="add_new_account_hint_请输入账户名称">请输入账户名称</string>
    <string name="add_new_account_保存">保存</string>
    <string name="add_new_account_出账日账单计入当期">出账日账单计入当期</string>
    <string name="add_new_account_当前欠款">当前欠款</string>
    <string name="add_new_account_总额度">总额度</string>
    <string name="add_new_account_日">日</string>
    <string name="add_new_account_每月">每月</string>
    <string name="add_new_account_添加账户">添加账户</string>
    <string name="add_new_account_计入总资产">计入总资产</string>
    <string name="add_new_account_账单日">账单日</string>
    <string name="add_new_account_账户余额">账户余额</string>
    <string name="add_new_account_账户名称">账户名称</string>
    <string name="add_new_account_账户备注">账户备注</string>
    <string name="add_new_account_账户币种">账户币种</string>
    <string name="add_new_account_返回">返回</string>
    <string name="add_new_account_还款日">还款日</string>
    <string name="add_new_account_选择类型">选择类型</string>
    <string name="add_new_account_选择银行">选择银行</string>
    <string name="app_name">LikeQianWang</string>
    <string name="budget_desc_分类图标">分类图标</string>
    <string name="budget_desc_删除分类预算">删除分类预算</string>
    <string name="budget_desc_返回">返回</string>
    <string name="budget_保存">保存</string>
    <string name="budget_分类预算设置">分类预算设置</string>
    <string name="budget_剩余预算">剩余预算</string>
    <string name="budget_取消">取消</string>
    <string name="budget_启用预算提醒">启用预算提醒</string>
    <string name="budget_周度预算">周度</string>
    <string name="budget_已用预算">已用预算</string>
    <string name="budget_年度预算">年度</string>
    <string name="budget_总预算">总预算</string>
    <string name="budget_总预算设置">总预算设置</string>
    <string name="budget_月度预算">月度</string>
    <string name="budget_添加分类预算">添加分类</string>
    <string name="budget_确定">确定</string>
    <string name="budget_请输入总预算金额">请输入总预算金额</string>
    <string name="budget_请输入预算金额">请输入预算金额</string>
    <string name="budget_超出预算">超出预算</string>
    <string name="budget_超预算提醒">超预算提醒</string>
    <string name="budget_选择分类">选择分类</string>
    <string name="budget_预算充足">预算充足</string>
    <string name="budget_预算周期">预算周期</string>
    <string name="budget_预算提醒设置">预算提醒设置</string>
    <string name="budget_预算紧张">预算紧张</string>
    <string name="budget_预算设置">预算设置</string>
    <string name="budget_预算进度">预算进度</string>
    <string name="budget_预警">预警</string>
    <string name="budget_预警阈值">预警阈值</string>
    <string name="in_out_总支出">总支出</string>
    <string name="in_out_总收入">总收入</string>
    <string name="in_out_结余">结余</string>
    <string name="inputKeyboard_0">0</string>
    <string name="inputKeyboard_1">1</string>
    <string name="inputKeyboard_2">2</string>
    <string name="inputKeyboard_3">3</string>
    <string name="inputKeyboard_4">4</string>
    <string name="inputKeyboard_5">5</string>
    <string name="inputKeyboard_6">6</string>
    <string name="inputKeyboard_7">7</string>
    <string name="inputKeyboard_8">8</string>
    <string name="inputKeyboard_9">9</string>
    <string name="inputKeyboard_AddAnother">再记</string>
    <string name="inputKeyboard_confirm">确定</string>
    <string name="inputKeyboard_dot">.</string>
    <string name="inputKeyboard_minus">-</string>
    <string name="inputKeyboard_plus">+</string>
    <string name="receipt_desc_主界面导航栏">主界面导航栏</string>
    <string name="receipt_desc_收支示意圆点">收支示意圆点</string>
    <string name="receipt_desc_收支预算组件导航栏">收支预算组件圆点导航</string>
    <string name="receipt_desc_新增">新增一笔记录</string>
    <string name="receipt_desc_标签图标">标签图标</string>
    <string name="receipt_desc_设置">用户设置</string>
    <string name="receipt_账本">默认账本</string>
    <string name="recording_dialog_desc_收支子类型列表">收支子类型列表</string>
    <string name="recording_labels">标记</string>
    <string name="recording_page_desc_关闭页面">关闭记录页面</string>
    <string name="recording_page_desc_收支转账导航栏">收支转账导航栏</string>
    <string name="recording_page_desc_类型图标">类型图标</string>
    <string name="recording_select_account">选择账户</string>
    <string name="recording_tags">标签</string>
    <string name="recording_transfer_dialog_desc_账户列表">账户列表</string>
    <string name="recording_transfer_select_transfer_in_account">请输入转入账户</string>
    <string name="recording_transfer_select_transfer_out_account">请输入转出账户</string>
    <string name="recording_输入收支备注">点此输入备注…</string>
    <string name="title_统计">统计</string>
    <string name="title_账单">账单</string>
    <string name="title_钱包">钱包</string>
    <string name="wallet_净资产">净资产</string>
    <string name="wallet_总负债">总负债</string>
    <string name="wallet_总资产">总资产</string>
    <string name="wallet_账户不计入资产">不计入</string>
    <string name="wallets_desc_账户图标">账户图标</string>
    <string name="wallets_desc_账户类型列表">账户类型列表</string>
    <string name="wallets_desc_返回选择类型">返回选择类型</string>
    <string name="wallets_desc_银行列表">银行列表</string>
    <string name="不计入收支">不计入收支</string>
    <string name="不计入预算">不计入预算</string>
    <string name="取消">取消</string>
    <string name="标签管理">标签管理</string>
    <string name="确定">确定</string>
    <style name="CustomBottomSheet" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">@android:color/white</item>
        <item name="android:elevation">16dp</item>
    </style>
    <style name="CustomBottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheet</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustNothing</item>
    </style>
    <style name="CustomShapeAppearanceBottomSheetDialog" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">20dp</item>
        <item name="cornerSizeTopLeft">20dp</item>
    </style>
    <style name="DatePickerStyle" parent="android:Widget.Material.DatePicker">
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textSize">16sp</item>
        <item name="colorControlNormal">@color/HuaQing</item>
        <item name="colorAccent">@color/HuaQing</item>
    </style>
    <style name="Theme.LikeQianWang" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/HuaQing</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">#00000000</item>
        
        <item name="android:navigationBarColor">#00000000</item>
    </style>
    <style name="TimePickerStyle" parent="android:Widget.Material.TimePicker">
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textSize">16sp</item>
        <item name="colorControlNormal">@color/HuaQing</item>
        <item name="colorAccent">@color/HuaQing</item>
    </style>
    <style name="circleIconStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="记账选项按键样式">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/widget_small_button_bg</item>
        <item name="backgroundTint">@color/white</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
    <style name="软键盘按键样式">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">54dp</item>
        <item name="android:layout_columnWeight">1</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/widget_small_button_bg</item>
        <item name="backgroundTint">@color/white</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
    <style name="隐藏小白条">
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
</resources>