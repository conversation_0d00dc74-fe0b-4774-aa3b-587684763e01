// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.TextView;
import android.widget.TimePicker;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogDateTimePickerViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView DateTimePickerCancel;

  @NonNull
  public final TextView DateTimePickerConfirm;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final DatePicker recordingPageDatePicker;

  @NonNull
  public final ConstraintLayout recordingPageDateTimePickerView;

  @NonNull
  public final TimePicker recordingPageTimePicker;

  private DialogDateTimePickerViewBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView DateTimePickerCancel, @NonNull TextView DateTimePickerConfirm,
      @NonNull View dragHandle, @NonNull DatePicker recordingPageDatePicker,
      @NonNull ConstraintLayout recordingPageDateTimePickerView,
      @NonNull TimePicker recordingPageTimePicker) {
    this.rootView = rootView;
    this.DateTimePickerCancel = DateTimePickerCancel;
    this.DateTimePickerConfirm = DateTimePickerConfirm;
    this.dragHandle = dragHandle;
    this.recordingPageDatePicker = recordingPageDatePicker;
    this.recordingPageDateTimePickerView = recordingPageDateTimePickerView;
    this.recordingPageTimePicker = recordingPageTimePicker;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogDateTimePickerViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogDateTimePickerViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_date_time_picker_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogDateTimePickerViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.DateTimePicker_cancel;
      TextView DateTimePickerCancel = ViewBindings.findChildViewById(rootView, id);
      if (DateTimePickerCancel == null) {
        break missingId;
      }

      id = R.id.DateTimePicker_confirm;
      TextView DateTimePickerConfirm = ViewBindings.findChildViewById(rootView, id);
      if (DateTimePickerConfirm == null) {
        break missingId;
      }

      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.recording_page_DatePicker;
      DatePicker recordingPageDatePicker = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageDatePicker == null) {
        break missingId;
      }

      ConstraintLayout recordingPageDateTimePickerView = (ConstraintLayout) rootView;

      id = R.id.recording_page_TimePicker;
      TimePicker recordingPageTimePicker = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageTimePicker == null) {
        break missingId;
      }

      return new DialogDateTimePickerViewBinding((ConstraintLayout) rootView, DateTimePickerCancel,
          DateTimePickerConfirm, dragHandle, recordingPageDatePicker,
          recordingPageDateTimePickerView, recordingPageTimePicker);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
