// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAccountTypeCategoryBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView expandIcon;

  @NonNull
  public final TextView walletsAccountTypeCategoryName;

  @NonNull
  public final RecyclerView walletsAccountTypeItemList;

  private ItemAccountTypeCategoryBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageView expandIcon, @NonNull TextView walletsAccountTypeCategoryName,
      @NonNull RecyclerView walletsAccountTypeItemList) {
    this.rootView = rootView;
    this.expandIcon = expandIcon;
    this.walletsAccountTypeCategoryName = walletsAccountTypeCategoryName;
    this.walletsAccountTypeItemList = walletsAccountTypeItemList;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAccountTypeCategoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAccountTypeCategoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_account_type_category, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAccountTypeCategoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.expand_icon;
      ImageView expandIcon = ViewBindings.findChildViewById(rootView, id);
      if (expandIcon == null) {
        break missingId;
      }

      id = R.id.wallets_account_type_category_name;
      TextView walletsAccountTypeCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountTypeCategoryName == null) {
        break missingId;
      }

      id = R.id.wallets_account_type_item_list;
      RecyclerView walletsAccountTypeItemList = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountTypeItemList == null) {
        break missingId;
      }

      return new ItemAccountTypeCategoryBinding((ConstraintLayout) rootView, expandIcon,
          walletsAccountTypeCategoryName, walletsAccountTypeItemList);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
