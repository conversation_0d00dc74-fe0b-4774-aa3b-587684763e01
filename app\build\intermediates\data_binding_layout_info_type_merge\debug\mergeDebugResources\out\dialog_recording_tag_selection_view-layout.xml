<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_recording_tag_selection_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_recording_tag_selection_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/recording_page_tagSelectionView"><Targets><Target id="@+id/recording_page_tagSelectionView" tag="layout/dialog_recording_tag_selection_view_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="105" endOffset="51"/></Target><Target id="@+id/drag_handle" view="View"><Expressions/><location startLine="14" startOffset="4" endLine="22" endOffset="51"/></Target><Target id="@+id/TagSelection_confirm" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="36" endOffset="63"/></Target><Target id="@+id/TagSelection_cancel" view="TextView"><Expressions/><location startLine="38" startOffset="4" endLine="50" endOffset="69"/></Target><Target id="@+id/TagSelection_container" view="LinearLayout"><Expressions/><location startLine="53" startOffset="4" endLine="78" endOffset="18"/></Target><Target id="@+id/flexbox_selected_tags" view="com.google.android.flexbox.FlexboxLayout"><Expressions/><location startLine="69" startOffset="8" endLine="76" endOffset="48"/></Target><Target id="@+id/rv_TagSelection_categories" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="81" startOffset="4" endLine="88" endOffset="74"/></Target><Target id="@+id/tv_TagSelection_management" view="TextView"><Expressions/><location startLine="90" startOffset="4" endLine="103" endOffset="50"/></Target></Targets></Layout>