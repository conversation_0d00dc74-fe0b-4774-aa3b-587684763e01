// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleRecordingCategoryItemViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivRecordingCategoryItemIcon;

  @NonNull
  public final TextView tvRecordingCategoryItemName;

  private StyleRecordingCategoryItemViewBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView ivRecordingCategoryItemIcon,
      @NonNull TextView tvRecordingCategoryItemName) {
    this.rootView = rootView;
    this.ivRecordingCategoryItemIcon = ivRecordingCategoryItemIcon;
    this.tvRecordingCategoryItemName = tvRecordingCategoryItemName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleRecordingCategoryItemViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleRecordingCategoryItemViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_recording_category_item_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleRecordingCategoryItemViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_recording_category_item_icon;
      ImageView ivRecordingCategoryItemIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivRecordingCategoryItemIcon == null) {
        break missingId;
      }

      id = R.id.tv_recording_category_item_name;
      TextView tvRecordingCategoryItemName = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingCategoryItemName == null) {
        break missingId;
      }

      return new StyleRecordingCategoryItemViewBinding((LinearLayout) rootView,
          ivRecordingCategoryItemIcon, tvRecordingCategoryItemName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
