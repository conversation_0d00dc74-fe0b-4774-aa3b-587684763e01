// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecordingTransferViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnRecordingPageTransferSwapAccounts;

  @NonNull
  public final ShapeableImageView ivRecordingPageTransferFromAccountIcon;

  @NonNull
  public final ShapeableImageView ivRecordingPageTransferToAccountIcon;

  @NonNull
  public final Guideline recordingPageGuidelineVerticalCenter;

  @NonNull
  public final ConstraintLayout recordingPageTransferFromAccount;

  @NonNull
  public final ConstraintLayout recordingPageTransferToAccount;

  @NonNull
  public final TextView tvRecordingPageTransferFromAccountBalance;

  @NonNull
  public final TextView tvRecordingPageTransferFromAccountName;

  @NonNull
  public final TextView tvRecordingPageTransferToAccountBalance;

  @NonNull
  public final TextView tvRecordingPageTransferToAccountName;

  private ItemRecordingTransferViewBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnRecordingPageTransferSwapAccounts,
      @NonNull ShapeableImageView ivRecordingPageTransferFromAccountIcon,
      @NonNull ShapeableImageView ivRecordingPageTransferToAccountIcon,
      @NonNull Guideline recordingPageGuidelineVerticalCenter,
      @NonNull ConstraintLayout recordingPageTransferFromAccount,
      @NonNull ConstraintLayout recordingPageTransferToAccount,
      @NonNull TextView tvRecordingPageTransferFromAccountBalance,
      @NonNull TextView tvRecordingPageTransferFromAccountName,
      @NonNull TextView tvRecordingPageTransferToAccountBalance,
      @NonNull TextView tvRecordingPageTransferToAccountName) {
    this.rootView = rootView;
    this.btnRecordingPageTransferSwapAccounts = btnRecordingPageTransferSwapAccounts;
    this.ivRecordingPageTransferFromAccountIcon = ivRecordingPageTransferFromAccountIcon;
    this.ivRecordingPageTransferToAccountIcon = ivRecordingPageTransferToAccountIcon;
    this.recordingPageGuidelineVerticalCenter = recordingPageGuidelineVerticalCenter;
    this.recordingPageTransferFromAccount = recordingPageTransferFromAccount;
    this.recordingPageTransferToAccount = recordingPageTransferToAccount;
    this.tvRecordingPageTransferFromAccountBalance = tvRecordingPageTransferFromAccountBalance;
    this.tvRecordingPageTransferFromAccountName = tvRecordingPageTransferFromAccountName;
    this.tvRecordingPageTransferToAccountBalance = tvRecordingPageTransferToAccountBalance;
    this.tvRecordingPageTransferToAccountName = tvRecordingPageTransferToAccountName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecordingTransferViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecordingTransferViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recording_transfer_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecordingTransferViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_recording_page_transfer_swap_accounts;
      MaterialButton btnRecordingPageTransferSwapAccounts = ViewBindings.findChildViewById(rootView, id);
      if (btnRecordingPageTransferSwapAccounts == null) {
        break missingId;
      }

      id = R.id.iv_recording_page_transfer_from_account_icon;
      ShapeableImageView ivRecordingPageTransferFromAccountIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivRecordingPageTransferFromAccountIcon == null) {
        break missingId;
      }

      id = R.id.iv_recording_page_transfer_to_account_icon;
      ShapeableImageView ivRecordingPageTransferToAccountIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivRecordingPageTransferToAccountIcon == null) {
        break missingId;
      }

      id = R.id.recording_page_guideline_vertical_center;
      Guideline recordingPageGuidelineVerticalCenter = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageGuidelineVerticalCenter == null) {
        break missingId;
      }

      id = R.id.recording_page_transfer_from_account;
      ConstraintLayout recordingPageTransferFromAccount = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageTransferFromAccount == null) {
        break missingId;
      }

      id = R.id.recording_page_transfer_to_account;
      ConstraintLayout recordingPageTransferToAccount = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageTransferToAccount == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_transfer_from_account_balance;
      TextView tvRecordingPageTransferFromAccountBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageTransferFromAccountBalance == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_transfer_from_account_name;
      TextView tvRecordingPageTransferFromAccountName = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageTransferFromAccountName == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_transfer_to_account_balance;
      TextView tvRecordingPageTransferToAccountBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageTransferToAccountBalance == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_transfer_to_account_name;
      TextView tvRecordingPageTransferToAccountName = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageTransferToAccountName == null) {
        break missingId;
      }

      return new ItemRecordingTransferViewBinding((ConstraintLayout) rootView,
          btnRecordingPageTransferSwapAccounts, ivRecordingPageTransferFromAccountIcon,
          ivRecordingPageTransferToAccountIcon, recordingPageGuidelineVerticalCenter,
          recordingPageTransferFromAccount, recordingPageTransferToAccount,
          tvRecordingPageTransferFromAccountBalance, tvRecordingPageTransferFromAccountName,
          tvRecordingPageTransferToAccountBalance, tvRecordingPageTransferToAccountName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
