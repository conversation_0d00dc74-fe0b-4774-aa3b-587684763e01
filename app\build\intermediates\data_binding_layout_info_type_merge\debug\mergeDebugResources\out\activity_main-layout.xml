<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/container"><Targets><Target id="@+id/container" tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="33" endOffset="51"/></Target><Target id="@+id/initViewPager_tabs" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="9" startOffset="4" endLine="23" endOffset="36"/></Target><Target id="@+id/initViewPager_fragments" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="25" startOffset="4" endLine="31" endOffset="51"/></Target></Targets></Layout>