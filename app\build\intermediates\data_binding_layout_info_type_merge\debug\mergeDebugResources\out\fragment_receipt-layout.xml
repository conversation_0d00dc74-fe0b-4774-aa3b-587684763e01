<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_receipt" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\fragment_receipt.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_receipt_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="107" endOffset="51"/></Target><Target id="@+id/receipt_Receipts" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/receipt_Setting" view="ImageView"><Expressions/><location startLine="20" startOffset="4" endLine="32" endOffset="35"/></Target><Target id="@+id/receipt_Year_Month" view="TextView"><Expressions/><location startLine="34" startOffset="4" endLine="45" endOffset="68"/></Target><Target id="@+id/receipt_InOut_Budget_Container" view="com.example.likeqianwang.Utils.NestedScrollableHost"><Expressions/><location startLine="47" startOffset="4" endLine="62" endOffset="57"/></Target><Target id="@+id/receipt_InOut_Budget_Widget" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="54" startOffset="8" endLine="60" endOffset="51"/></Target><Target id="@+id/receipt_InOut_Budget_WidgetTabs" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="64" startOffset="4" endLine="77" endOffset="36"/></Target><Target id="@+id/receipt_Daily_InOut_List" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="79" startOffset="4" endLine="88" endOffset="56"/></Target><Target id="@+id/button_Add" view="ImageButton"><Expressions/><location startLine="90" startOffset="4" endLine="105" endOffset="33"/></Target></Targets></Layout>