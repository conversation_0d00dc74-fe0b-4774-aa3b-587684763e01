// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleRecordingTransferAccountSelectionItemViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ShapeableImageView ivRecordingPageTransferAccountItemIcon;

  @NonNull
  public final TextView tvRecordingPageTransferAccountBalance;

  @NonNull
  public final TextView tvRecordingPageTransferAccountName;

  private StyleRecordingTransferAccountSelectionItemViewBinding(@NonNull LinearLayout rootView,
      @NonNull ShapeableImageView ivRecordingPageTransferAccountItemIcon,
      @NonNull TextView tvRecordingPageTransferAccountBalance,
      @NonNull TextView tvRecordingPageTransferAccountName) {
    this.rootView = rootView;
    this.ivRecordingPageTransferAccountItemIcon = ivRecordingPageTransferAccountItemIcon;
    this.tvRecordingPageTransferAccountBalance = tvRecordingPageTransferAccountBalance;
    this.tvRecordingPageTransferAccountName = tvRecordingPageTransferAccountName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleRecordingTransferAccountSelectionItemViewBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleRecordingTransferAccountSelectionItemViewBinding inflate(
      @NonNull LayoutInflater inflater, @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_recording_transfer_account_selection_item_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleRecordingTransferAccountSelectionItemViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_recording_page_transfer_account_item_icon;
      ShapeableImageView ivRecordingPageTransferAccountItemIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivRecordingPageTransferAccountItemIcon == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_transfer_account_balance;
      TextView tvRecordingPageTransferAccountBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageTransferAccountBalance == null) {
        break missingId;
      }

      id = R.id.tv_recording_page_transfer_account_name;
      TextView tvRecordingPageTransferAccountName = ViewBindings.findChildViewById(rootView, id);
      if (tvRecordingPageTransferAccountName == null) {
        break missingId;
      }

      return new StyleRecordingTransferAccountSelectionItemViewBinding((LinearLayout) rootView,
          ivRecordingPageTransferAccountItemIcon, tvRecordingPageTransferAccountBalance,
          tvRecordingPageTransferAccountName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
