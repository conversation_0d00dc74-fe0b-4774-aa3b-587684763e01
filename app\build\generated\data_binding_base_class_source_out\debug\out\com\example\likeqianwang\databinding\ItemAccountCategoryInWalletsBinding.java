// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAccountCategoryInWalletsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout accountCategory;

  @NonNull
  public final ImageView ivAccountExpandIcon;

  @NonNull
  public final RecyclerView rvAccountList;

  @NonNull
  public final TextView tvAccountCategoryName;

  @NonNull
  public final TextView tvAccountCategoryTotal;

  private ItemAccountCategoryInWalletsBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout accountCategory, @NonNull ImageView ivAccountExpandIcon,
      @NonNull RecyclerView rvAccountList, @NonNull TextView tvAccountCategoryName,
      @NonNull TextView tvAccountCategoryTotal) {
    this.rootView = rootView;
    this.accountCategory = accountCategory;
    this.ivAccountExpandIcon = ivAccountExpandIcon;
    this.rvAccountList = rvAccountList;
    this.tvAccountCategoryName = tvAccountCategoryName;
    this.tvAccountCategoryTotal = tvAccountCategoryTotal;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAccountCategoryInWalletsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAccountCategoryInWalletsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_account_category_in_wallets, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAccountCategoryInWalletsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.account_category;
      LinearLayout accountCategory = ViewBindings.findChildViewById(rootView, id);
      if (accountCategory == null) {
        break missingId;
      }

      id = R.id.iv_account_expand_icon;
      ImageView ivAccountExpandIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivAccountExpandIcon == null) {
        break missingId;
      }

      id = R.id.rv_account_list;
      RecyclerView rvAccountList = ViewBindings.findChildViewById(rootView, id);
      if (rvAccountList == null) {
        break missingId;
      }

      id = R.id.tv_account_category_name;
      TextView tvAccountCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountCategoryName == null) {
        break missingId;
      }

      id = R.id.tv_account_category_total;
      TextView tvAccountCategoryTotal = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountCategoryTotal == null) {
        break missingId;
      }

      return new ItemAccountCategoryInWalletsBinding((LinearLayout) rootView, accountCategory,
          ivAccountExpandIcon, rvAccountList, tvAccountCategoryName, tvAccountCategoryTotal);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
