// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleNavbarItemViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView navImage;

  @NonNull
  public final TextView navText;

  private StyleNavbarItemViewBinding(@NonNull LinearLayout rootView, @NonNull ImageView navImage,
      @NonNull TextView navText) {
    this.rootView = rootView;
    this.navImage = navImage;
    this.navText = navText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleNavbarItemViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleNavbarItemViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_navbar_item_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleNavbarItemViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.nav_image;
      ImageView navImage = ViewBindings.findChildViewById(rootView, id);
      if (navImage == null) {
        break missingId;
      }

      id = R.id.nav_text;
      TextView navText = ViewBindings.findChildViewById(rootView, id);
      if (navText == null) {
        break missingId;
      }

      return new StyleNavbarItemViewBinding((LinearLayout) rootView, navImage, navText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
