[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_ic_category_food.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\ic_category_food.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_add_new_account_list_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_add_new_account_list_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_frag_recording_transfer_out_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\frag_recording_transfer_out_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_transportation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_transportation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_airplane.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_airplane.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_frag_recording_transfer_swap_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\frag_recording_transfer_swap_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_diet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_diet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\anim_recording_page_error_shake.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\anim\\recording_page_error_shake.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_recording_transfer_account_selection_item_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_recording_transfer_account_selection_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_recording_tag_selection_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_recording_tag_selection_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_gdb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_gdb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_midnight_snack.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_midnight_snack.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_recording_category_item_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_recording_category_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_account_icon_e_cny.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\account_icon_e_cny.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_lunch.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_lunch.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\color_main_activity_selector_selection_state.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\color\\main_activity_selector_selection_state.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_account_icon_huabei.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\account_icon_huabei.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_frag_receipt_add_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\frag_receipt_add_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_housing_fund.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_housing_fund.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_activity_budget_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_budget_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_vegetable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_vegetable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_cmb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_cmb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_account_item_in_wallets.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_account_item_in_wallets.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_journey.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_journey.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_dialog_bottom_sheet_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\dialog_bottom_sheet_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_widget_common_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\widget_common_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_daily_in_out_detail_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_daily_in_out_detail_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_item_account_category_in_wallets.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_account_category_in_wallets.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_express_delivery.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_express_delivery.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_recording_tag_selectable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_recording_tag_selectable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\color_selector_tag_text.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\color\\selector_tag_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_style_edittext_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\style_edittext_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_recording_account_selection_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_recording_account_selection_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_stats.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_stats.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_wallets_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_wallets_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_budget_status_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\budget_status_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_bonus.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_bonus.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_item_account_type_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_account_type_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_financing.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_financing.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_education.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_education.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_widget_tag_item_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\widget_tag_item_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_widget_icon_dot_enable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\widget_icon_dot_enable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_widget_in_and_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\widget_in_and_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_dinner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_dinner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_stock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_stock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_tag_management_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_tag_management_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_stats_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_stats_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_daily_in_out_detail_tag_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_daily_in_out_detail_tag_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_account_icon_wechat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\account_icon_wechat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_trip_allowance.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_trip_allowance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_makeup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_makeup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_clothes.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_clothes.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_budget_remaining_progress_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\budget_remaining_progress_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_boc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_boc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_wallets.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_wallets.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_account_icon_jd.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\account_icon_jd.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_frag_receipt_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\frag_receipt_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_widget_icon_dot_disable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\widget_icon_dot_disable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_bus.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_bus.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_spdb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_spdb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_style_tag_color_picker_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\style_tag_color_picker_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_widget_icon_dot.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\widget_icon_dot.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_fruit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_fruit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_daily_in_out_list_item_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_daily_in_out_list_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_receipts_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_receipts_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_widget_icon_dot_combine.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\widget_icon_dot_combine.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_item_budget_category_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_budget_category_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_account_icon_debit_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\account_icon_debit_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_style_tag_management_item_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\style_tag_management_item_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_selector_tag_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\selector_tag_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_citic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_citic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\color_switchmaterial_thumb_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\color\\switchmaterial_thumb_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_bob.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_bob.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_item_recording_tag_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_recording_tag_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_entertainment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_entertainment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_account_type_item_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_account_type_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_navbar_item_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_navbar_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_breakfast.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_breakfast.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_activity_recording_page.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_recording_page.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_others.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_others.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_cib.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_cib.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_hxb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_hxb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_budget_progress_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\budget_progress_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_salary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_salary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_taxi.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_taxi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_arrow_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_arrow_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_receipts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_receipts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_item_budget_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_budget_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_button_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\button_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_activity_fill_new_account_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_fill_new_account_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_psbc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_psbc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_frag_receipt_add_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\frag_receipt_add_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_account_icon_credit_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\account_icon_credit_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_cmbc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_cmbc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_ccb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_ccb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_include_in_stats_budget_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_include_in_stats_budget_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_setting_in_recording.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_setting_in_recording.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_widget_budget.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\widget_budget.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_recording_account_selection_item_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_recording_account_selection_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_bocom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_bocom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_budget_category_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_budget_category_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_charge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_charge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_bike.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_bike.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_item_tag_management_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_tag_management_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_account_icon_cash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\account_icon_cash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_fragment_stats.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\fragment_stats.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_medicine.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_medicine.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_abc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_abc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_account_icon_alipay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\account_icon_alipay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_widget_divider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\widget_divider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_investment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_juice.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_juice.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_credit_date_picker_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_credit_date_picker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_bank_icbc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_bank_icbc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_tag_edit_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_tag_edit_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_sports.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_sports.xml"}, {"merged": "com.example.likeqianwang.app-debug-50:/drawable_budget_remaining_progress_bar.xml.flat", "source": "com.example.likeqianwang.app-main-52:/drawable/budget_remaining_progress_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_fund.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_fund.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_arrow_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_arrow_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_frag_recording_transfer_in_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\frag_recording_transfer_in_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_account_bank_item_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_account_bank_item_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_train.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_train.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\color_switchmaterial_track_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\color\\switchmaterial_track_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_recording_transfer_account_selection_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_recording_transfer_account_selection_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_item_recording_transfer_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_recording_transfer_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_recording_subcategory_list_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_recording_subcategory_list_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_item_recording_in_out_category_list_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\item_recording_in_out_category_list_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_frag_wallets_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\frag_wallets_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_interest.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_interest.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_swap.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_swap.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_subway.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_subway.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_snacks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_snacks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_widget_small_button_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\widget_small_button_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_recyclerview_item_selected_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\recyclerview_item_selected_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_button_outline_grey.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\button_outline_grey.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_fragment_wallet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\fragment_wallet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_mobile_credit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_mobile_credit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_activity_tag_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\activity_tag_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_add_new_account_bank_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_add_new_account_bank_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_dialog_date_time_picker_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\dialog_date_time_picker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\drawable_icon_recording_haircut.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\drawable\\icon_recording_haircut.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_style_tag_color_picker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\style_tag_color_picker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-debug-50:\\layout_fragment_receipt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.likeqianwang.app-main-52:\\layout\\fragment_receipt.xml"}]