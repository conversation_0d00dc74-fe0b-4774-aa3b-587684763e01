// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTagManagementBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivAddTag;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final LinearLayout llEmptyState;

  @NonNull
  public final RecyclerView rvTagManagement;

  private ActivityTagManagementBinding(@NonNull LinearLayout rootView, @NonNull ImageView ivAddTag,
      @NonNull ImageView ivBack, @NonNull LinearLayout llEmptyState,
      @NonNull RecyclerView rvTagManagement) {
    this.rootView = rootView;
    this.ivAddTag = ivAddTag;
    this.ivBack = ivBack;
    this.llEmptyState = llEmptyState;
    this.rvTagManagement = rvTagManagement;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTagManagementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTagManagementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_tag_management, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTagManagementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_add_tag;
      ImageView ivAddTag = ViewBindings.findChildViewById(rootView, id);
      if (ivAddTag == null) {
        break missingId;
      }

      id = R.id.iv_back;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ll_empty_state;
      LinearLayout llEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (llEmptyState == null) {
        break missingId;
      }

      id = R.id.rv_tag_management;
      RecyclerView rvTagManagement = ViewBindings.findChildViewById(rootView, id);
      if (rvTagManagement == null) {
        break missingId;
      }

      return new ActivityTagManagementBinding((LinearLayout) rootView, ivAddTag, ivBack,
          llEmptyState, rvTagManagement);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
