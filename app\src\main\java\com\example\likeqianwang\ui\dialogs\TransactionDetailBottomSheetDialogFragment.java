package com.example.likeqianwang.ui.dialogs;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.R;
import com.example.likeqianwang.RecordingPageActivity;
import com.example.likeqianwang.ViewModel.ReceiptViewModel;
import com.example.likeqianwang.ViewModel.TransactionCategoryViewModel;
import com.example.likeqianwang.ViewModel.AccountViewModel;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Locale;

public class TransactionDetailBottomSheetDialogFragment extends BottomSheetDialogFragment {

    private static final String ARG_TRANSACTION = "transaction";
    private static final String TAG = "TransactionDetail";

    // UI组件
    private ImageView typeIcon;
    private TextView amountText;
    private TextView typeText;
    private ImageView closeButton;
    private ImageView categoryIcon;
    private TextView categoryName;
    private TextView accountName;
    private TextView dateTime;
    private LinearLayout remarkContainer;
    private TextView remarkText;
    private LinearLayout tagsContainer;
    private RecyclerView tagsList;
    private TextView editButton;
    private TextView deleteButton;
    private TextView refundButton;

    // 数据
    private Transactions transaction;
    private ReceiptViewModel receiptViewModel;
    private TransactionCategoryViewModel categoryViewModel;
    private AccountViewModel accountViewModel;

    // 格式化工具
    private final DecimalFormat amountFormatter = new DecimalFormat("¥#,##0.00");
    private final SimpleDateFormat dateTimeFormatter = new SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.getDefault());

    public static TransactionDetailBottomSheetDialogFragment newInstance(Transactions transaction) {
        TransactionDetailBottomSheetDialogFragment fragment = new TransactionDetailBottomSheetDialogFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_TRANSACTION, transaction);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            transaction = (Transactions) getArguments().getSerializable(ARG_TRANSACTION);
        }
        
        // 初始化ViewModels
        receiptViewModel = new ViewModelProvider(requireActivity()).get(ReceiptViewModel.class);
        categoryViewModel = new ViewModelProvider(requireActivity()).get(TransactionCategoryViewModel.class);
        accountViewModel = new ViewModelProvider(requireActivity()).get(AccountViewModel.class);
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_transaction_detail_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupClickListeners();
        loadTransactionData();
    }

    private void initViews(View view) {
        typeIcon = view.findViewById(R.id.transaction_detail_type_icon);
        amountText = view.findViewById(R.id.transaction_detail_amount);
        typeText = view.findViewById(R.id.transaction_detail_type_text);
        closeButton = view.findViewById(R.id.transaction_detail_close);
        categoryIcon = view.findViewById(R.id.transaction_detail_category_icon);
        categoryName = view.findViewById(R.id.transaction_detail_category_name);
        accountName = view.findViewById(R.id.transaction_detail_account_name);
        dateTime = view.findViewById(R.id.transaction_detail_datetime);
        remarkContainer = view.findViewById(R.id.transaction_detail_remark_container);
        remarkText = view.findViewById(R.id.transaction_detail_remark);
        tagsContainer = view.findViewById(R.id.transaction_detail_tags_container);
        tagsList = view.findViewById(R.id.transaction_detail_tags_list);
        editButton = view.findViewById(R.id.transaction_detail_edit_button);
        deleteButton = view.findViewById(R.id.transaction_detail_delete_button);
        refundButton = view.findViewById(R.id.transaction_detail_refund_button);
    }

    private void setupClickListeners() {
        closeButton.setOnClickListener(v -> dismiss());
        editButton.setOnClickListener(v -> editTransaction());
        deleteButton.setOnClickListener(v -> showDeleteConfirmation());
        refundButton.setOnClickListener(v -> showRefundDialog());
    }

    private void loadTransactionData() {
        if (transaction == null) {
            Log.e(TAG, "Transaction is null");
            dismiss();
            return;
        }

        try {
            // 设置基本信息
            amountText.setText(amountFormatter.format(transaction.getAmount()));
            dateTime.setText(dateTimeFormatter.format(transaction.getTransactionDate()));

            // 设置交易类型
            String typeString = getTransactionTypeString(transaction.getType());
            typeText.setText(typeString);

            // 设置交易类型图标和颜色
            setupTransactionTypeDisplay();

            // 加载分类信息
            loadCategoryInfo();

            // 加载账户信息
            loadAccountInfo();

            // 设置备注
            setupRemark();

            // 设置标签（如果有的话）
            setupTags();

            // 根据交易类型调整按钮显示
            adjustButtonsForTransactionType();

        } catch (Exception e) {
            Log.e(TAG, "Error loading transaction data", e);
            Toast.makeText(getContext(), "加载交易详情失败", Toast.LENGTH_SHORT).show();
        }
    }

    private String getTransactionTypeString(String type) {
        switch (type) {
            case "INCOME":
                return "收入";
            case "EXPENSE":
                return "支出";
            case "TRANSFER":
                return "转账";
            default:
                return "未知";
        }
    }

    private void setupTransactionTypeDisplay() {
        int iconRes;
        int colorRes;

        switch (transaction.getType()) {
            case "INCOME":
                iconRes = R.drawable.ic_income;
                colorRes = R.color.HuaQing;
                break;
            case "EXPENSE":
                iconRes = R.drawable.ic_expense;
                colorRes = R.color.ChaHuaHong;
                break;
            case "TRANSFER":
                iconRes = R.drawable.ic_transfer;
                colorRes = R.color.black;
                break;
            default:
                iconRes = R.drawable.ic_category_food;
                colorRes = R.color.black;
                break;
        }

        try {
            typeIcon.setImageResource(iconRes);
            amountText.setTextColor(getResources().getColor(colorRes, null));
        } catch (Exception e) {
            Log.e(TAG, "Error setting transaction type display", e);
        }
    }

    private void loadCategoryInfo() {
        if (transaction.getCategoryId() != null) {
            categoryViewModel.getCategoryById(transaction.getCategoryId())
                    .observe(getViewLifecycleOwner(), category -> {
                        if (category != null) {
                            categoryName.setText(category.getCategoryName());
                            try {
                                categoryIcon.setImageResource(category.getCategoryIcon());
                            } catch (Exception e) {
                                Log.e(TAG, "Error setting category icon", e);
                                categoryIcon.setImageResource(R.drawable.ic_category_food);
                            }
                        } else {
                            categoryName.setText("未分类");
                            categoryIcon.setImageResource(R.drawable.ic_category_food);
                        }
                    });
        } else {
            categoryName.setText("未分类");
            categoryIcon.setImageResource(R.drawable.ic_category_food);
        }
    }

    private void loadAccountInfo() {
        if (transaction.getAccountId() != null) {
            accountViewModel.getAccountById(transaction.getAccountId())
                    .observe(getViewLifecycleOwner(), account -> {
                        if (account != null) {
                            accountName.setText(account.getAccountName());
                        } else {
                            accountName.setText("未知账户");
                        }
                    });
        } else {
            accountName.setText("未知账户");
        }
    }

    private void setupRemark() {
        if (transaction.getRemark() != null && !transaction.getRemark().trim().isEmpty()) {
            remarkText.setText(transaction.getRemark());
            remarkContainer.setVisibility(View.VISIBLE);
        } else {
            remarkContainer.setVisibility(View.GONE);
        }
    }

    private void setupTags() {
        // TODO: 实现标签显示逻辑
        // 目前隐藏标签容器，等待标签功能实现
        tagsContainer.setVisibility(View.GONE);
    }

    private void adjustButtonsForTransactionType() {
        // 根据交易类型调整按钮显示
        if ("TRANSFER".equals(transaction.getType())) {
            // 转账不显示退款按钮
            refundButton.setVisibility(View.GONE);
        } else if ("INCOME".equals(transaction.getType())) {
            // 收入显示为"撤销"而不是"退款"
            refundButton.setText("撤销");
        }
    }

    private void editTransaction() {
        try {
            Intent editIntent = new Intent(getActivity(), RecordingPageActivity.class);
            editIntent.putExtra("EDIT_TRANSACTION", transaction);
            editIntent.putExtra("EDIT_MODE", true);
            startActivity(editIntent);
            dismiss();
        } catch (Exception e) {
            Log.e(TAG, "Error starting edit activity", e);
            Toast.makeText(getContext(), "打开编辑页面失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void showDeleteConfirmation() {
        try {
            new AlertDialog.Builder(requireContext())
                    .setTitle(getString(R.string.receipt_确认删除))
                    .setMessage(getString(R.string.receipt_删除交易确认))
                    .setPositiveButton(getString(R.string.receipt_确定), (dialog, which) -> deleteTransaction())
                    .setNegativeButton(getString(R.string.receipt_取消), null)
                    .show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing delete confirmation", e);
            Toast.makeText(getContext(), "显示确认对话框失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void deleteTransaction() {
        try {
            receiptViewModel.deleteTransaction(transaction, new ReceiptViewModel.TransactionOperationCallback() {
                @Override
                public void onSuccess() {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(getContext(), "交易已删除", Toast.LENGTH_SHORT).show();
                            dismiss();
                        });
                    }
                }

                @Override
                public void onError(String error) {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(getContext(), "删除失败: " + error, Toast.LENGTH_SHORT).show();
                        });
                    }
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error deleting transaction", e);
            Toast.makeText(getContext(), "删除交易失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void showRefundDialog() {
        try {
            RefundBottomSheetDialogFragment refundDialog =
                    RefundBottomSheetDialogFragment.newInstance(transaction);
            refundDialog.show(getChildFragmentManager(), "RefundDialog");
        } catch (Exception e) {
            Log.e(TAG, "Error showing refund dialog", e);
            Toast.makeText(getContext(), "打开退款对话框失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        try {
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up full height", e);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理资源
        receiptViewModel = null;
        categoryViewModel = null;
        accountViewModel = null;
    }
}
