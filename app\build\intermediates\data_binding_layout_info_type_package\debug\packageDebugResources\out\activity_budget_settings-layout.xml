<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_budget_settings" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\activity_budget_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_budget_settings_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="332" endOffset="51"/></Target><Target id="@+id/budget_settings_toolbar" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="10" startOffset="4" endLine="58" endOffset="55"/></Target><Target id="@+id/budget_settings_back" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="29" endOffset="37"/></Target><Target id="@+id/budget_settings_title" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="42" endOffset="55"/></Target><Target id="@+id/budget_settings_save" view="TextView"><Expressions/><location startLine="44" startOffset="8" endLine="56" endOffset="55"/></Target><Target id="@+id/budget_period_group" view="RadioGroup"><Expressions/><location startLine="82" startOffset="12" endLine="114" endOffset="24"/></Target><Target id="@+id/budget_period_monthly" view="RadioButton"><Expressions/><location startLine="89" startOffset="16" endLine="96" endOffset="45"/></Target><Target id="@+id/budget_period_weekly" view="RadioButton"><Expressions/><location startLine="98" startOffset="16" endLine="104" endOffset="45"/></Target><Target id="@+id/budget_period_yearly" view="RadioButton"><Expressions/><location startLine="106" startOffset="16" endLine="112" endOffset="45"/></Target><Target id="@+id/budget_total_amount" view="EditText"><Expressions/><location startLine="153" startOffset="24" endLine="163" endOffset="53"/></Target><Target id="@+id/budget_total_alert_threshold" view="SeekBar"><Expressions/><location startLine="181" startOffset="24" endLine="189" endOffset="51"/></Target><Target id="@+id/budget_total_alert_percentage" view="TextView"><Expressions/><location startLine="191" startOffset="24" endLine="197" endOffset="53"/></Target><Target id="@+id/budget_add_category_budget" view="TextView"><Expressions/><location startLine="234" startOffset="24" endLine="242" endOffset="53"/></Target><Target id="@+id/budget_category_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="246" startOffset="20" endLine="251" endOffset="64"/></Target><Target id="@+id/budget_notification_switch" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="293" startOffset="24" endLine="297" endOffset="52"/></Target><Target id="@+id/budget_over_budget_alert_switch" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="316" startOffset="24" endLine="320" endOffset="52"/></Target></Targets></Layout>