<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_tag_management" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\activity_tag_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_tag_management_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="91" endOffset="14"/></Target><Target id="@+id/iv_back" view="ImageView"><Expressions/><location startLine="16" startOffset="8" endLine="26" endOffset="61"/></Target><Target id="@+id/iv_add_tag" view="ImageView"><Expressions/><location startLine="40" startOffset="8" endLine="50" endOffset="61"/></Target><Target id="@+id/rv_tag_management" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="55" startOffset="4" endLine="62" endOffset="63"/></Target><Target id="@+id/ll_empty_state" view="LinearLayout"><Expressions/><location startLine="65" startOffset="4" endLine="89" endOffset="18"/></Target></Targets></Layout>