<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_wallets_account_bank_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:padding="1dp"
        app:shapeAppearanceOverlay="@style/circleIconStyle"
        app:strokeColor="@color/YinBai"
        app:strokeWidth="1dp" />

    <TextView
        android:id="@+id/tv_wallets_account_bank_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:textColor="@color/black"
        android:textSize="16sp" />

</LinearLayout>