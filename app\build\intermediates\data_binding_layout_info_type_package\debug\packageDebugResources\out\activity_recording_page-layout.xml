<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_recording_page" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\activity_recording_page.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/recording_page"><Targets><Target id="@+id/recording_page" tag="layout/activity_recording_page_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="295" endOffset="51"/></Target><Target id="@+id/recording_page_close_page" view="ImageView"><Expressions/><location startLine="12" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/recording_page_InOutTrans_tabs" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="22" startOffset="4" endLine="33" endOffset="36"/></Target><Target id="@+id/recording_page_InOutTrans_views" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="35" startOffset="4" endLine="42" endOffset="79"/></Target><Target id="@+id/recording_page_Select_widget" view="HorizontalScrollView"><Expressions/><location startLine="44" startOffset="4" endLine="107" endOffset="26"/></Target><Target id="@+id/recording_page_SelectAccount" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="60" startOffset="12" endLine="70" endOffset="53"/></Target><Target id="@+id/recording_page_SelectTime" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="72" startOffset="12" endLine="82" endOffset="35"/></Target><Target id="@+id/recording_page_SelectTags" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="84" startOffset="12" endLine="93" endOffset="53"/></Target><Target id="@+id/recording_page_SelectOthers" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="95" startOffset="12" endLine="103" endOffset="53"/></Target><Target id="@+id/recording_page_NumAndRemark_widget" view="LinearLayout"><Expressions/><location startLine="109" startOffset="4" endLine="152" endOffset="18"/></Target><Target id="@+id/tv_recording_page_recordingNum" view="TextView"><Expressions/><location startLine="124" startOffset="8" endLine="136" endOffset="38"/></Target><Target id="@+id/et_recording_page_remark" view="EditText"><Expressions/><location startLine="139" startOffset="8" endLine="150" endOffset="49"/></Target><Target id="@+id/recording_page_inputKeyboard" view="GridLayout"><Expressions/><location startLine="155" startOffset="4" endLine="293" endOffset="16"/></Target><Target id="@+id/btn1" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="173" startOffset="8" endLine="178" endOffset="52"/></Target><Target id="@+id/btn2" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="180" startOffset="8" endLine="186" endOffset="52"/></Target><Target id="@+id/btn3" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="188" startOffset="8" endLine="194" endOffset="52"/></Target><Target id="@+id/btnBackspace" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="196" startOffset="8" endLine="201" endOffset="30"/></Target><Target id="@+id/btn4" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="204" startOffset="8" endLine="210" endOffset="52"/></Target><Target id="@+id/btn5" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="212" startOffset="8" endLine="216" endOffset="52"/></Target><Target id="@+id/btn6" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="218" startOffset="8" endLine="222" endOffset="52"/></Target><Target id="@+id/btnPlus" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="224" startOffset="8" endLine="230" endOffset="55"/></Target><Target id="@+id/btn7" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="233" startOffset="8" endLine="239" endOffset="52"/></Target><Target id="@+id/btn8" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="241" startOffset="8" endLine="245" endOffset="52"/></Target><Target id="@+id/btn9" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="247" startOffset="8" endLine="251" endOffset="52"/></Target><Target id="@+id/btnMinus" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="253" startOffset="8" endLine="259" endOffset="56"/></Target><Target id="@+id/btnAddAnother" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="262" startOffset="8" endLine="267" endOffset="61"/></Target><Target id="@+id/btn0" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="269" startOffset="8" endLine="275" endOffset="52"/></Target><Target id="@+id/btnDot" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="277" startOffset="8" endLine="283" endOffset="54"/></Target><Target id="@+id/btnEqual" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="285" startOffset="8" endLine="292" endOffset="52"/></Target></Targets></Layout>