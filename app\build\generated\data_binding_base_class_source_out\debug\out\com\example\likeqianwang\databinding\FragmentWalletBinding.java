// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWalletBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView tvWidgetAssetLiabilityNetAsset;

  @NonNull
  public final TextView tvWidgetAssetLiabilityNetAssetBalance;

  @NonNull
  public final TextView tvWidgetAssetLiabilityTotalAsset;

  @NonNull
  public final TextView tvWidgetAssetLiabilityTotalAssetBalance;

  @NonNull
  public final TextView tvWidgetAssetLiabilityTotalLiability;

  @NonNull
  public final TextView tvWidgetAssetLiabilityTotalLiabilityBalance;

  @NonNull
  public final RecyclerView walletsAccountCategoryList;

  @NonNull
  public final ImageView walletsAddNewAccountButton;

  @NonNull
  public final FrameLayout walletsAssetSummaryContainer;

  @NonNull
  public final TextView walletsTitle;

  private FragmentWalletBinding(@NonNull LinearLayout rootView,
      @NonNull TextView tvWidgetAssetLiabilityNetAsset,
      @NonNull TextView tvWidgetAssetLiabilityNetAssetBalance,
      @NonNull TextView tvWidgetAssetLiabilityTotalAsset,
      @NonNull TextView tvWidgetAssetLiabilityTotalAssetBalance,
      @NonNull TextView tvWidgetAssetLiabilityTotalLiability,
      @NonNull TextView tvWidgetAssetLiabilityTotalLiabilityBalance,
      @NonNull RecyclerView walletsAccountCategoryList,
      @NonNull ImageView walletsAddNewAccountButton,
      @NonNull FrameLayout walletsAssetSummaryContainer, @NonNull TextView walletsTitle) {
    this.rootView = rootView;
    this.tvWidgetAssetLiabilityNetAsset = tvWidgetAssetLiabilityNetAsset;
    this.tvWidgetAssetLiabilityNetAssetBalance = tvWidgetAssetLiabilityNetAssetBalance;
    this.tvWidgetAssetLiabilityTotalAsset = tvWidgetAssetLiabilityTotalAsset;
    this.tvWidgetAssetLiabilityTotalAssetBalance = tvWidgetAssetLiabilityTotalAssetBalance;
    this.tvWidgetAssetLiabilityTotalLiability = tvWidgetAssetLiabilityTotalLiability;
    this.tvWidgetAssetLiabilityTotalLiabilityBalance = tvWidgetAssetLiabilityTotalLiabilityBalance;
    this.walletsAccountCategoryList = walletsAccountCategoryList;
    this.walletsAddNewAccountButton = walletsAddNewAccountButton;
    this.walletsAssetSummaryContainer = walletsAssetSummaryContainer;
    this.walletsTitle = walletsTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWalletBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWalletBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_wallet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWalletBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_widget_asset_liability_netAsset;
      TextView tvWidgetAssetLiabilityNetAsset = ViewBindings.findChildViewById(rootView, id);
      if (tvWidgetAssetLiabilityNetAsset == null) {
        break missingId;
      }

      id = R.id.tv_widget_asset_liability_netAsset_balance;
      TextView tvWidgetAssetLiabilityNetAssetBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvWidgetAssetLiabilityNetAssetBalance == null) {
        break missingId;
      }

      id = R.id.tv_widget_asset_liability_totalAsset;
      TextView tvWidgetAssetLiabilityTotalAsset = ViewBindings.findChildViewById(rootView, id);
      if (tvWidgetAssetLiabilityTotalAsset == null) {
        break missingId;
      }

      id = R.id.tv_widget_asset_liability_totalAsset_balance;
      TextView tvWidgetAssetLiabilityTotalAssetBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvWidgetAssetLiabilityTotalAssetBalance == null) {
        break missingId;
      }

      id = R.id.tv_widget_asset_liability_totalLiability;
      TextView tvWidgetAssetLiabilityTotalLiability = ViewBindings.findChildViewById(rootView, id);
      if (tvWidgetAssetLiabilityTotalLiability == null) {
        break missingId;
      }

      id = R.id.tv_widget_asset_liability_totalLiability_balance;
      TextView tvWidgetAssetLiabilityTotalLiabilityBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvWidgetAssetLiabilityTotalLiabilityBalance == null) {
        break missingId;
      }

      id = R.id.wallets_account_category_list;
      RecyclerView walletsAccountCategoryList = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountCategoryList == null) {
        break missingId;
      }

      id = R.id.wallets_add_new_account_button;
      ImageView walletsAddNewAccountButton = ViewBindings.findChildViewById(rootView, id);
      if (walletsAddNewAccountButton == null) {
        break missingId;
      }

      id = R.id.wallets_asset_summary_container;
      FrameLayout walletsAssetSummaryContainer = ViewBindings.findChildViewById(rootView, id);
      if (walletsAssetSummaryContainer == null) {
        break missingId;
      }

      id = R.id.wallets_title;
      TextView walletsTitle = ViewBindings.findChildViewById(rootView, id);
      if (walletsTitle == null) {
        break missingId;
      }

      return new FragmentWalletBinding((LinearLayout) rootView, tvWidgetAssetLiabilityNetAsset,
          tvWidgetAssetLiabilityNetAssetBalance, tvWidgetAssetLiabilityTotalAsset,
          tvWidgetAssetLiabilityTotalAssetBalance, tvWidgetAssetLiabilityTotalLiability,
          tvWidgetAssetLiabilityTotalLiabilityBalance, walletsAccountCategoryList,
          walletsAddNewAccountButton, walletsAssetSummaryContainer, walletsTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
