<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:padding="8dp">

    <ImageView
        android:id="@+id/iv_recording_category_item_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:padding="8dp"
        android:contentDescription="@string/recording_page_desc_类型图标" />

    <TextView
        android:id="@+id/tv_recording_category_item_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textSize="12sp"
        tools:text="类型名称" />

</LinearLayout>