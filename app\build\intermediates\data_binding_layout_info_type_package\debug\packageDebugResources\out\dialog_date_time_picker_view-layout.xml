<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_date_time_picker_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_date_time_picker_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/recording_page_DateTimePickerView"><Targets><Target id="@+id/recording_page_DateTimePickerView" tag="layout/dialog_date_time_picker_view_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="51"/></Target><Target id="@+id/drag_handle" view="View"><Expressions/><location startLine="13" startOffset="4" endLine="21" endOffset="51"/></Target><Target id="@+id/DateTimePicker_confirm" view="TextView"><Expressions/><location startLine="23" startOffset="4" endLine="35" endOffset="55"/></Target><Target id="@+id/DateTimePicker_cancel" view="TextView"><Expressions/><location startLine="37" startOffset="4" endLine="49" endOffset="71"/></Target><Target id="@+id/recording_page_DatePicker" view="DatePicker"><Expressions/><location startLine="51" startOffset="4" endLine="60" endOffset="74"/></Target><Target id="@+id/recording_page_TimePicker" view="TimePicker"><Expressions/><location startLine="62" startOffset="4" endLine="70" endOffset="77"/></Target></Targets></Layout>