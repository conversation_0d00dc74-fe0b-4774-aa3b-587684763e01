// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class WidgetBudgetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView widgetBudget;

  @NonNull
  public final TextView widgetBudget1;

  @NonNull
  public final TextView widgetBudget2;

  @NonNull
  public final TextView widgetBudget3;

  @NonNull
  public final ProgressBar widgetBudget4;

  @NonNull
  public final TextView widgetBudget5;

  @NonNull
  public final TextView widgetBudget6;

  @NonNull
  public final View widgetBudget7;

  @NonNull
  public final TextView widgetBudget8;

  private WidgetBudgetBinding(@NonNull LinearLayout rootView, @NonNull TextView widgetBudget,
      @NonNull TextView widgetBudget1, @NonNull TextView widgetBudget2,
      @NonNull TextView widgetBudget3, @NonNull ProgressBar widgetBudget4,
      @NonNull TextView widgetBudget5, @NonNull TextView widgetBudget6, @NonNull View widgetBudget7,
      @NonNull TextView widgetBudget8) {
    this.rootView = rootView;
    this.widgetBudget = widgetBudget;
    this.widgetBudget1 = widgetBudget1;
    this.widgetBudget2 = widgetBudget2;
    this.widgetBudget3 = widgetBudget3;
    this.widgetBudget4 = widgetBudget4;
    this.widgetBudget5 = widgetBudget5;
    this.widgetBudget6 = widgetBudget6;
    this.widgetBudget7 = widgetBudget7;
    this.widgetBudget8 = widgetBudget8;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static WidgetBudgetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static WidgetBudgetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.widget_budget, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static WidgetBudgetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.widget_budget_剩余预算;
      TextView widgetBudget = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget == null) {
        break missingId;
      }

      id = R.id.widget_budget_剩余预算金额;
      TextView widgetBudget1 = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget1 == null) {
        break missingId;
      }

      id = R.id.widget_budget_已用预算;
      TextView widgetBudget2 = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget2 == null) {
        break missingId;
      }

      id = R.id.widget_budget_使用百分比;
      TextView widgetBudget3 = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget3 == null) {
        break missingId;
      }

      id = R.id.widget_budget_进度条;
      ProgressBar widgetBudget4 = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget4 == null) {
        break missingId;
      }

      id = R.id.widget_budget_已用金额;
      TextView widgetBudget5 = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget5 == null) {
        break missingId;
      }

      id = R.id.widget_budget_总预算金额;
      TextView widgetBudget6 = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget6 == null) {
        break missingId;
      }

      id = R.id.widget_budget_状态指示器;
      View widgetBudget7 = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget7 == null) {
        break missingId;
      }

      id = R.id.widget_budget_状态文本;
      TextView widgetBudget8 = ViewBindings.findChildViewById(rootView, id);
      if (widgetBudget8 == null) {
        break missingId;
      }

      return new WidgetBudgetBinding((LinearLayout) rootView, widgetBudget, widgetBudget1,
          widgetBudget2, widgetBudget3, widgetBudget4, widgetBudget5, widgetBudget6, widgetBudget7,
          widgetBudget8);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
