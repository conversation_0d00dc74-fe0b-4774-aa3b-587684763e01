<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/dialog_bottom_sheet_background"
    android:padding="24dp">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="添加标签"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="24dp"
        android:layout_gravity="center_horizontal" />

    <!-- 标签名称 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="标签名称"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_tag_name"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:hint="请输入标签名称"
        android:textSize="16sp"
        android:background="@drawable/style_edittext_bg"
        android:padding="12dp"
        android:layout_marginBottom="16dp"
        android:maxLength="10" />

    <!-- 标签分类 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="标签分类"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/spinner_tag_category"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/style_edittext_bg"
        android:layout_marginBottom="16dp" />

    <!-- 新分类输入 -->
    <LinearLayout
        android:id="@+id/ll_new_category"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="新分类名称"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/et_new_category"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="请输入新分类名称"
            android:textSize="16sp"
            android:background="@drawable/style_edittext_bg"
            android:padding="12dp"
            android:layout_marginBottom="16dp"
            android:maxLength="8" />

    </LinearLayout>

    <!-- 标签颜色 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="标签颜色"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_color_picker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        tools:listitem="@layout/style_tag_color_picker" />

    <!-- 按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="取消"
            android:textColor="@color/grey"
            android:background="@drawable/button_outline_grey"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="保存"
            android:textColor="@color/white"
            android:background="@drawable/button_primary"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>