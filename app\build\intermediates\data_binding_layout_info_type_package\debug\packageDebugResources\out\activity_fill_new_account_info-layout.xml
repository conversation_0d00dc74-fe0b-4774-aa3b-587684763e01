<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_fill_new_account_info" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\activity_fill_new_account_info.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/wallets_fill_new_account_info"><Targets><Target id="@+id/wallets_fill_new_account_info" tag="layout/activity_fill_new_account_info_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="464" endOffset="51"/></Target><Target id="@+id/tv_wallets_back" view="TextView"><Expressions/><location startLine="12" startOffset="4" endLine="23" endOffset="75"/></Target><Target id="@+id/tv_wallets_fill_info_title" view="TextView"><Expressions/><location startLine="25" startOffset="4" endLine="36" endOffset="51"/></Target><Target id="@+id/tv_wallets_save" view="TextView"><Expressions/><location startLine="38" startOffset="4" endLine="49" endOffset="75"/></Target><Target id="@+id/wallets_account_basic_info" view="LinearLayout"><Expressions/><location startLine="52" startOffset="4" endLine="204" endOffset="18"/></Target><Target id="@+id/wallets_account_type" view="LinearLayout"><Expressions/><location startLine="65" startOffset="8" endLine="99" endOffset="22"/></Target><Target id="@+id/iv_wallets_account_icon" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="75" startOffset="12" endLine="82" endOffset="39"/></Target><Target id="@+id/tv_wallets_account_name" view="TextView"><Expressions/><location startLine="88" startOffset="12" endLine="97" endOffset="48"/></Target><Target id="@+id/et_wallets_account_input_name" view="EditText"><Expressions/><location startLine="119" startOffset="12" endLine="131" endOffset="53"/></Target><Target id="@+id/wallets_account_input_balance_area" view="LinearLayout"><Expressions/><location startLine="135" startOffset="8" endLine="168" endOffset="22"/></Target><Target id="@+id/et_wallets_account_input_balance" view="EditText"><Expressions/><location startLine="154" startOffset="12" endLine="166" endOffset="53"/></Target><Target id="@+id/et_wallets_account_input_remark" view="EditText"><Expressions/><location startLine="188" startOffset="12" endLine="200" endOffset="53"/></Target><Target id="@+id/wallets_account_credit_info" view="LinearLayout"><Expressions/><location startLine="206" startOffset="4" endLine="382" endOffset="18"/></Target><Target id="@+id/et_wallets_account_input_total_credit" view="EditText"><Expressions/><location startLine="237" startOffset="12" endLine="249" endOffset="53"/></Target><Target id="@+id/et_wallets_account_input_current_credit" view="EditText"><Expressions/><location startLine="271" startOffset="12" endLine="283" endOffset="53"/></Target><Target id="@+id/et_wallets_account_input_statement_date" view="TextView"><Expressions/><location startLine="305" startOffset="12" endLine="314" endOffset="48"/></Target><Target id="@+id/et_wallets_account_input_due_date" view="TextView"><Expressions/><location startLine="336" startOffset="12" endLine="345" endOffset="48"/></Target><Target id="@+id/switch_wallets_account_due_date_in_current_period" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="370" startOffset="12" endLine="378" endOffset="67"/></Target><Target id="@+id/wallets_account_currency_selection" view="LinearLayout"><Expressions/><location startLine="396" startOffset="8" endLine="427" endOffset="22"/></Target><Target id="@+id/tv_wallets_account_currency" view="TextView"><Expressions/><location startLine="415" startOffset="12" endLine="425" endOffset="48"/></Target><Target id="@+id/switch_wallets_account_includeInAsset" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="450" startOffset="12" endLine="458" endOffset="67"/></Target></Targets></Layout>