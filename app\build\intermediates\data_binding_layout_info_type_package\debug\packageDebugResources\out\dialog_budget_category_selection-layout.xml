<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_budget_category_selection" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_budget_category_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_budget_category_selection_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="56" endOffset="14"/></Target><Target id="@+id/budget_category_selection_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="19" startOffset="4" endLine="24" endOffset="35"/></Target><Target id="@+id/budget_category_selection_cancel" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="37"/></Target><Target id="@+id/budget_category_selection_confirm" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="52" endOffset="38"/></Target></Targets></Layout>