// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckedTextView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogIncludeInStatsBudgetViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView IncludeInStatsBudgetCancel;

  @NonNull
  public final TextView IncludeInStatsBudgetConfirm;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final CheckedTextView recordingPageIncludeInBudget;

  @NonNull
  public final CheckedTextView recordingPageIncludeInStats;

  @NonNull
  public final ConstraintLayout recordingPageIncludeInStatsAndBudgetView;

  private DialogIncludeInStatsBudgetViewBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView IncludeInStatsBudgetCancel, @NonNull TextView IncludeInStatsBudgetConfirm,
      @NonNull View dragHandle, @NonNull CheckedTextView recordingPageIncludeInBudget,
      @NonNull CheckedTextView recordingPageIncludeInStats,
      @NonNull ConstraintLayout recordingPageIncludeInStatsAndBudgetView) {
    this.rootView = rootView;
    this.IncludeInStatsBudgetCancel = IncludeInStatsBudgetCancel;
    this.IncludeInStatsBudgetConfirm = IncludeInStatsBudgetConfirm;
    this.dragHandle = dragHandle;
    this.recordingPageIncludeInBudget = recordingPageIncludeInBudget;
    this.recordingPageIncludeInStats = recordingPageIncludeInStats;
    this.recordingPageIncludeInStatsAndBudgetView = recordingPageIncludeInStatsAndBudgetView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogIncludeInStatsBudgetViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogIncludeInStatsBudgetViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_include_in_stats_budget_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogIncludeInStatsBudgetViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.IncludeInStatsBudget_cancel;
      TextView IncludeInStatsBudgetCancel = ViewBindings.findChildViewById(rootView, id);
      if (IncludeInStatsBudgetCancel == null) {
        break missingId;
      }

      id = R.id.IncludeInStatsBudget_confirm;
      TextView IncludeInStatsBudgetConfirm = ViewBindings.findChildViewById(rootView, id);
      if (IncludeInStatsBudgetConfirm == null) {
        break missingId;
      }

      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.recording_page_includeInBudget;
      CheckedTextView recordingPageIncludeInBudget = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageIncludeInBudget == null) {
        break missingId;
      }

      id = R.id.recording_page_includeInStats;
      CheckedTextView recordingPageIncludeInStats = ViewBindings.findChildViewById(rootView, id);
      if (recordingPageIncludeInStats == null) {
        break missingId;
      }

      ConstraintLayout recordingPageIncludeInStatsAndBudgetView = (ConstraintLayout) rootView;

      return new DialogIncludeInStatsBudgetViewBinding((ConstraintLayout) rootView,
          IncludeInStatsBudgetCancel, IncludeInStatsBudgetConfirm, dragHandle,
          recordingPageIncludeInBudget, recordingPageIncludeInStats,
          recordingPageIncludeInStatsAndBudgetView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
