<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".ui.budget_settings.BudgetSettingsActivity">

    <!-- 顶部工具栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/budget_settings_toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/white"
        android:elevation="2dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/budget_settings_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/budget_desc_返回"
            android:padding="12dp"
            android:src="@drawable/ic_arrow_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/black" />

        <TextView
            android:id="@+id/budget_settings_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/budget_预算设置"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/budget_settings_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="12dp"
            android:text="@string/budget_保存"
            android:textColor="@color/HuaQing"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/budget_settings_toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 预算周期选择 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/budget_预算周期"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <RadioGroup
                android:id="@+id/budget_period_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/budget_period_monthly"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:checked="true"
                    android:text="@string/budget_月度预算"
                    android:textSize="14sp" />

                <RadioButton
                    android:id="@+id/budget_period_weekly"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/budget_周度预算"
                    android:textSize="14sp" />

                <RadioButton
                    android:id="@+id/budget_period_yearly"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/budget_年度预算"
                    android:textSize="14sp" />

            </RadioGroup>

            <!-- 总预算设置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="@string/budget_总预算设置"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="￥"
                            android:textColor="@color/black"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <EditText
                            android:id="@+id/budget_total_amount"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="@string/budget_请输入总预算金额"
                            android:inputType="numberDecimal"
                            android:padding="8dp"
                            android:textColor="@color/black"
                            android:textSize="18sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/budget_预警阈值"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <SeekBar
                            android:id="@+id/budget_total_alert_threshold"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="12dp"
                            android:layout_weight="1"
                            android:max="100"
                            android:progress="80" />

                        <TextView
                            android:id="@+id/budget_total_alert_percentage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="80%"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 分类预算设置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/budget_分类预算设置"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/budget_add_category_budget"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:padding="8dp"
                            android:text="@string/budget_添加分类预算"
                            android:textColor="@color/HuaQing"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/budget_category_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 预算提醒设置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="@string/budget_预算提醒设置"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/budget_启用预算提醒"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/budget_notification_switch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/budget_超预算提醒"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/budget_over_budget_alert_switch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
