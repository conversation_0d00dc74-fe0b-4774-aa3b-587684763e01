// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddNewAccountBankSelectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final EditText etWalletsAccountBankSearchInput;

  @NonNull
  public final RecyclerView rvBankList;

  @NonNull
  public final TextView walletsAccountBack;

  @NonNull
  public final TextView walletsAccountBankSelectionTitle;

  @NonNull
  public final MaterialCardView walletsAccountSearchBank;

  @NonNull
  public final LinearLayout walletsAddNewAccountBankSelection;

  private DialogAddNewAccountBankSelectionBinding(@NonNull LinearLayout rootView,
      @NonNull View dragHandle, @NonNull EditText etWalletsAccountBankSearchInput,
      @NonNull RecyclerView rvBankList, @NonNull TextView walletsAccountBack,
      @NonNull TextView walletsAccountBankSelectionTitle,
      @NonNull MaterialCardView walletsAccountSearchBank,
      @NonNull LinearLayout walletsAddNewAccountBankSelection) {
    this.rootView = rootView;
    this.dragHandle = dragHandle;
    this.etWalletsAccountBankSearchInput = etWalletsAccountBankSearchInput;
    this.rvBankList = rvBankList;
    this.walletsAccountBack = walletsAccountBack;
    this.walletsAccountBankSelectionTitle = walletsAccountBankSelectionTitle;
    this.walletsAccountSearchBank = walletsAccountSearchBank;
    this.walletsAddNewAccountBankSelection = walletsAddNewAccountBankSelection;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddNewAccountBankSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddNewAccountBankSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_new_account_bank_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddNewAccountBankSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.et_wallets_account_bank_search_input;
      EditText etWalletsAccountBankSearchInput = ViewBindings.findChildViewById(rootView, id);
      if (etWalletsAccountBankSearchInput == null) {
        break missingId;
      }

      id = R.id.rv_bank_list;
      RecyclerView rvBankList = ViewBindings.findChildViewById(rootView, id);
      if (rvBankList == null) {
        break missingId;
      }

      id = R.id.wallets_account_back;
      TextView walletsAccountBack = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountBack == null) {
        break missingId;
      }

      id = R.id.wallets_account_bank_selection_title;
      TextView walletsAccountBankSelectionTitle = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountBankSelectionTitle == null) {
        break missingId;
      }

      id = R.id.wallets_account_search_bank;
      MaterialCardView walletsAccountSearchBank = ViewBindings.findChildViewById(rootView, id);
      if (walletsAccountSearchBank == null) {
        break missingId;
      }

      LinearLayout walletsAddNewAccountBankSelection = (LinearLayout) rootView;

      return new DialogAddNewAccountBankSelectionBinding((LinearLayout) rootView, dragHandle,
          etWalletsAccountBankSearchInput, rvBankList, walletsAccountBack,
          walletsAccountBankSelectionTitle, walletsAccountSearchBank,
          walletsAddNewAccountBankSelection);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
