// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogTagEditViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final EditText etNewCategory;

  @NonNull
  public final EditText etTagName;

  @NonNull
  public final LinearLayout llNewCategory;

  @NonNull
  public final RecyclerView rvColorPicker;

  @NonNull
  public final Spinner spinnerTagCategory;

  @NonNull
  public final TextView tvDialogTitle;

  private DialogTagEditViewBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnSave, @NonNull EditText etNewCategory, @NonNull EditText etTagName,
      @NonNull LinearLayout llNewCategory, @NonNull RecyclerView rvColorPicker,
      @NonNull Spinner spinnerTagCategory, @NonNull TextView tvDialogTitle) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.etNewCategory = etNewCategory;
    this.etTagName = etTagName;
    this.llNewCategory = llNewCategory;
    this.rvColorPicker = rvColorPicker;
    this.spinnerTagCategory = spinnerTagCategory;
    this.tvDialogTitle = tvDialogTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTagEditViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTagEditViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_tag_edit_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTagEditViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_save;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.et_new_category;
      EditText etNewCategory = ViewBindings.findChildViewById(rootView, id);
      if (etNewCategory == null) {
        break missingId;
      }

      id = R.id.et_tag_name;
      EditText etTagName = ViewBindings.findChildViewById(rootView, id);
      if (etTagName == null) {
        break missingId;
      }

      id = R.id.ll_new_category;
      LinearLayout llNewCategory = ViewBindings.findChildViewById(rootView, id);
      if (llNewCategory == null) {
        break missingId;
      }

      id = R.id.rv_color_picker;
      RecyclerView rvColorPicker = ViewBindings.findChildViewById(rootView, id);
      if (rvColorPicker == null) {
        break missingId;
      }

      id = R.id.spinner_tag_category;
      Spinner spinnerTagCategory = ViewBindings.findChildViewById(rootView, id);
      if (spinnerTagCategory == null) {
        break missingId;
      }

      id = R.id.tv_dialog_title;
      TextView tvDialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvDialogTitle == null) {
        break missingId;
      }

      return new DialogTagEditViewBinding((LinearLayout) rootView, btnCancel, btnSave,
          etNewCategory, etTagName, llNewCategory, rvColorPicker, spinnerTagCategory,
          tvDialogTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
