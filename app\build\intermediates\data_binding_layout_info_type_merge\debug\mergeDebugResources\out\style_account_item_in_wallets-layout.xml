<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="style_account_item_in_wallets" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\style_account_item_in_wallets.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/style_account_item_in_wallets_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="104" endOffset="51"/></Target><Target id="@+id/account_item_content" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="102" endOffset="18"/></Target><Target id="@+id/iv_account_item_icon" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="17" startOffset="8" endLine="25" endOffset="50"/></Target><Target id="@+id/tv_account_item_name" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="40" endOffset="38"/></Target><Target id="@+id/tv_account_item_due_time_remained" view="TextView"><Expressions/><location startLine="42" startOffset="12" endLine="49" endOffset="44"/></Target><Target id="@+id/tv_account_item_remark" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="59" endOffset="44"/></Target><Target id="@+id/tv_account_item_includeInAsset" view="TextView"><Expressions/><location startLine="61" startOffset="12" endLine="74" endOffset="44"/></Target><Target id="@+id/tv_account_item_balance" view="TextView"><Expressions/><location startLine="83" startOffset="12" endLine="90" endOffset="40"/></Target><Target id="@+id/tv_account_item_credit_remained" view="TextView"><Expressions/><location startLine="92" startOffset="12" endLine="98" endOffset="46"/></Target></Targets></Layout>