// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class StyleDailyInOutDetailViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView receiptDailyInOutAccount;

  @NonNull
  public final TextView receiptDailyInOutAmount;

  @NonNull
  public final LinearLayout receiptDailyInOutAmountAndAccount;

  @NonNull
  public final ImageView receiptDailyInOutIcon;

  @NonNull
  public final TextView receiptDailyInOutKind;

  @NonNull
  public final TextView receiptDailyInOutRemark;

  @NonNull
  public final LinearLayout receiptDailyInOutTaglist;

  private StyleDailyInOutDetailViewBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView receiptDailyInOutAccount, @NonNull TextView receiptDailyInOutAmount,
      @NonNull LinearLayout receiptDailyInOutAmountAndAccount,
      @NonNull ImageView receiptDailyInOutIcon, @NonNull TextView receiptDailyInOutKind,
      @NonNull TextView receiptDailyInOutRemark, @NonNull LinearLayout receiptDailyInOutTaglist) {
    this.rootView = rootView;
    this.receiptDailyInOutAccount = receiptDailyInOutAccount;
    this.receiptDailyInOutAmount = receiptDailyInOutAmount;
    this.receiptDailyInOutAmountAndAccount = receiptDailyInOutAmountAndAccount;
    this.receiptDailyInOutIcon = receiptDailyInOutIcon;
    this.receiptDailyInOutKind = receiptDailyInOutKind;
    this.receiptDailyInOutRemark = receiptDailyInOutRemark;
    this.receiptDailyInOutTaglist = receiptDailyInOutTaglist;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static StyleDailyInOutDetailViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static StyleDailyInOutDetailViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.style_daily_in_out_detail_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static StyleDailyInOutDetailViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.receipt_Daily_InOut_account;
      TextView receiptDailyInOutAccount = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutAccount == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_amount;
      TextView receiptDailyInOutAmount = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutAmount == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_AmountAndAccount;
      LinearLayout receiptDailyInOutAmountAndAccount = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutAmountAndAccount == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_icon;
      ImageView receiptDailyInOutIcon = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutIcon == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_kind;
      TextView receiptDailyInOutKind = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutKind == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_remark;
      TextView receiptDailyInOutRemark = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutRemark == null) {
        break missingId;
      }

      id = R.id.receipt_Daily_InOut_taglist;
      LinearLayout receiptDailyInOutTaglist = ViewBindings.findChildViewById(rootView, id);
      if (receiptDailyInOutTaglist == null) {
        break missingId;
      }

      return new StyleDailyInOutDetailViewBinding((ConstraintLayout) rootView,
          receiptDailyInOutAccount, receiptDailyInOutAmount, receiptDailyInOutAmountAndAccount,
          receiptDailyInOutIcon, receiptDailyInOutKind, receiptDailyInOutRemark,
          receiptDailyInOutTaglist);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
