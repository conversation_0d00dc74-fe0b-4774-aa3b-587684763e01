// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import com.google.android.flexbox.FlexboxLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogRecordingTagSelectionViewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView TagSelectionCancel;

  @NonNull
  public final TextView TagSelectionConfirm;

  @NonNull
  public final LinearLayout TagSelectionContainer;

  @NonNull
  public final View dragHandle;

  @NonNull
  public final FlexboxLayout flexboxSelectedTags;

  @NonNull
  public final ConstraintLayout recordingPageTagSelectionView;

  @NonNull
  public final RecyclerView rvTagSelectionCategories;

  @NonNull
  public final TextView tvTagSelectionManagement;

  private DialogRecordingTagSelectionViewBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView TagSelectionCancel, @NonNull TextView TagSelectionConfirm,
      @NonNull LinearLayout TagSelectionContainer, @NonNull View dragHandle,
      @NonNull FlexboxLayout flexboxSelectedTags,
      @NonNull ConstraintLayout recordingPageTagSelectionView,
      @NonNull RecyclerView rvTagSelectionCategories, @NonNull TextView tvTagSelectionManagement) {
    this.rootView = rootView;
    this.TagSelectionCancel = TagSelectionCancel;
    this.TagSelectionConfirm = TagSelectionConfirm;
    this.TagSelectionContainer = TagSelectionContainer;
    this.dragHandle = dragHandle;
    this.flexboxSelectedTags = flexboxSelectedTags;
    this.recordingPageTagSelectionView = recordingPageTagSelectionView;
    this.rvTagSelectionCategories = rvTagSelectionCategories;
    this.tvTagSelectionManagement = tvTagSelectionManagement;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogRecordingTagSelectionViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogRecordingTagSelectionViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_recording_tag_selection_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogRecordingTagSelectionViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.TagSelection_cancel;
      TextView TagSelectionCancel = ViewBindings.findChildViewById(rootView, id);
      if (TagSelectionCancel == null) {
        break missingId;
      }

      id = R.id.TagSelection_confirm;
      TextView TagSelectionConfirm = ViewBindings.findChildViewById(rootView, id);
      if (TagSelectionConfirm == null) {
        break missingId;
      }

      id = R.id.TagSelection_container;
      LinearLayout TagSelectionContainer = ViewBindings.findChildViewById(rootView, id);
      if (TagSelectionContainer == null) {
        break missingId;
      }

      id = R.id.drag_handle;
      View dragHandle = ViewBindings.findChildViewById(rootView, id);
      if (dragHandle == null) {
        break missingId;
      }

      id = R.id.flexbox_selected_tags;
      FlexboxLayout flexboxSelectedTags = ViewBindings.findChildViewById(rootView, id);
      if (flexboxSelectedTags == null) {
        break missingId;
      }

      ConstraintLayout recordingPageTagSelectionView = (ConstraintLayout) rootView;

      id = R.id.rv_TagSelection_categories;
      RecyclerView rvTagSelectionCategories = ViewBindings.findChildViewById(rootView, id);
      if (rvTagSelectionCategories == null) {
        break missingId;
      }

      id = R.id.tv_TagSelection_management;
      TextView tvTagSelectionManagement = ViewBindings.findChildViewById(rootView, id);
      if (tvTagSelectionManagement == null) {
        break missingId;
      }

      return new DialogRecordingTagSelectionViewBinding((ConstraintLayout) rootView,
          TagSelectionCancel, TagSelectionConfirm, TagSelectionContainer, dragHandle,
          flexboxSelectedTags, recordingPageTagSelectionView, rvTagSelectionCategories,
          tvTagSelectionManagement);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
