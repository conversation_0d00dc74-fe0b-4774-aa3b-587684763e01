// Generated by view binder compiler. Do not edit!
package com.example.likeqianwang.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.likeqianwang.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogBudgetCategorySelectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView budgetCategorySelectionCancel;

  @NonNull
  public final TextView budgetCategorySelectionConfirm;

  @NonNull
  public final RecyclerView budgetCategorySelectionList;

  private DialogBudgetCategorySelectionBinding(@NonNull LinearLayout rootView,
      @NonNull TextView budgetCategorySelectionCancel,
      @NonNull TextView budgetCategorySelectionConfirm,
      @NonNull RecyclerView budgetCategorySelectionList) {
    this.rootView = rootView;
    this.budgetCategorySelectionCancel = budgetCategorySelectionCancel;
    this.budgetCategorySelectionConfirm = budgetCategorySelectionConfirm;
    this.budgetCategorySelectionList = budgetCategorySelectionList;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogBudgetCategorySelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogBudgetCategorySelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_budget_category_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogBudgetCategorySelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.budget_category_selection_cancel;
      TextView budgetCategorySelectionCancel = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategorySelectionCancel == null) {
        break missingId;
      }

      id = R.id.budget_category_selection_confirm;
      TextView budgetCategorySelectionConfirm = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategorySelectionConfirm == null) {
        break missingId;
      }

      id = R.id.budget_category_selection_list;
      RecyclerView budgetCategorySelectionList = ViewBindings.findChildViewById(rootView, id);
      if (budgetCategorySelectionList == null) {
        break missingId;
      }

      return new DialogBudgetCategorySelectionBinding((LinearLayout) rootView,
          budgetCategorySelectionCancel, budgetCategorySelectionConfirm,
          budgetCategorySelectionList);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
