<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_budget_category" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\item_budget_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_budget_category_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="108" endOffset="51"/></Target><Target id="@+id/budget_category_icon" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="48"/></Target><Target id="@+id/budget_category_name" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="33" endOffset="25"/></Target><Target id="@+id/budget_category_amount_layout" view="LinearLayout"><Expressions/><location startLine="35" startOffset="4" endLine="94" endOffset="18"/></Target><Target id="@+id/budget_category_amount" view="EditText"><Expressions/><location startLine="55" startOffset="8" endLine="65" endOffset="37"/></Target><Target id="@+id/budget_category_alert_threshold" view="EditText"><Expressions/><location startLine="75" startOffset="8" endLine="85" endOffset="37"/></Target><Target id="@+id/budget_category_delete" view="ImageView"><Expressions/><location startLine="96" startOffset="4" endLine="106" endOffset="38"/></Target></Targets></Layout>