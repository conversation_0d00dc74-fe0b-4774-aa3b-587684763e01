<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_include_in_stats_budget_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_include_in_stats_budget_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/recording_page_includeInStatsAndBudgetView"><Targets><Target id="@+id/recording_page_includeInStatsAndBudgetView" tag="layout/dialog_include_in_stats_budget_view_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="84" endOffset="51"/></Target><Target id="@+id/drag_handle" view="View"><Expressions/><location startLine="14" startOffset="4" endLine="22" endOffset="51"/></Target><Target id="@+id/IncludeInStatsBudget_confirm" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="36" endOffset="63"/></Target><Target id="@+id/IncludeInStatsBudget_cancel" view="TextView"><Expressions/><location startLine="38" startOffset="4" endLine="50" endOffset="77"/></Target><Target id="@+id/recording_page_includeInStats" view="CheckedTextView"><Expressions/><location startLine="52" startOffset="4" endLine="66" endOffset="80"/></Target><Target id="@+id/recording_page_includeInBudget" view="CheckedTextView"><Expressions/><location startLine="68" startOffset="4" endLine="82" endOffset="81"/></Target></Targets></Layout>