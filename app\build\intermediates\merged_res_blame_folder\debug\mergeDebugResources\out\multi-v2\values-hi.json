{"logs": [{"outputFile": "com.example.likeqianwang.app-mergeDebugResources-48:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2baf259cf119559651ffb6fa809681cc\\transformed\\core-1.13.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3429,3527,3630,3735,3836,3949,4055,10148", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3522,3625,3730,3831,3944,4050,4177,10244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0be8fe7a00cc17258e0156ab1fb27859\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,9828", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,9905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\49b3057b7c0d26ae3cd69c88f8b7ab6e\\transformed\\navigation-ui-2.8.9\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,113", "endOffsets": "154,268"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9531,9635", "endColumns": "103,113", "endOffsets": "9630,9744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\36a84f081851bde3ce49fbf8d9f79604\\transformed\\material-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1086,1151,1245,1314,1373,1458,1521,1584,1642,1707,1768,1829,1935,1993,2053,2112,2182,2298,2377,2468,2561,2659,2739,2873,2948,3024,3161,3258,3356,3413,3468,3534,3604,3681,3752,3837,3905,3981,4062,4140,4241,4327,4414,4511,4610,4684,4754,4858,4912,4999,5066,5156,5248,5310,5374,5437,5503,5608,5718,5819,5926,5987,6046,6125,6210,6290", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "254,332,408,489,596,692,799,931,1014,1081,1146,1240,1309,1368,1453,1516,1579,1637,1702,1763,1824,1930,1988,2048,2107,2177,2293,2372,2463,2556,2654,2734,2868,2943,3019,3156,3253,3351,3408,3463,3529,3599,3676,3747,3832,3900,3976,4057,4135,4236,4322,4409,4506,4605,4679,4749,4853,4907,4994,5061,5151,5243,5305,5369,5432,5498,5603,5713,5814,5921,5982,6041,6120,6205,6285,6358"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2991,3069,3145,3226,3333,4182,4289,4421,4504,4571,4636,4730,4799,4858,4943,5006,5069,5127,5192,5253,5314,5420,5478,5538,5597,5667,5783,5862,5953,6046,6144,6224,6358,6433,6509,6646,6743,6841,6898,6953,7019,7089,7166,7237,7322,7390,7466,7547,7625,7726,7812,7899,7996,8095,8169,8239,8343,8397,8484,8551,8641,8733,8795,8859,8922,8988,9093,9203,9304,9411,9472,9749,9910,9995,10075", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "304,3064,3140,3221,3328,3424,4284,4416,4499,4566,4631,4725,4794,4853,4938,5001,5064,5122,5187,5248,5309,5415,5473,5533,5592,5662,5778,5857,5948,6041,6139,6219,6353,6428,6504,6641,6738,6836,6893,6948,7014,7084,7161,7232,7317,7385,7461,7542,7620,7721,7807,7894,7991,8090,8164,8234,8338,8392,8479,8546,8636,8728,8790,8854,8917,8983,9088,9198,9299,9406,9467,9526,9823,9990,10070,10143"}}]}]}