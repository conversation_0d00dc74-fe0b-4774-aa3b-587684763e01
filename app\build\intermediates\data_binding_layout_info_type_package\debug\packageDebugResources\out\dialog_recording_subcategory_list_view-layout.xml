<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_recording_subcategory_list_view" modulePackage="com.example.likeqianwang" filePath="app\src\main\res\layout\dialog_recording_subcategory_list_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_recording_subcategory_list_view_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="61" endOffset="14"/></Target><Target id="@+id/drag_handle" view="View"><Expressions/><location startLine="14" startOffset="4" endLine="20" endOffset="38"/></Target><Target id="@+id/iv_recording_subcategory_close" view="ImageView"><Expressions/><location startLine="27" startOffset="8" endLine="35" endOffset="83"/></Target><Target id="@+id/tv_recording_subcategory_title" view="TextView"><Expressions/><location startLine="37" startOffset="8" endLine="48" endOffset="29"/></Target><Target id="@+id/rv_recording_subcategory_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="53" startOffset="4" endLine="59" endOffset="76"/></Target></Targets></Layout>