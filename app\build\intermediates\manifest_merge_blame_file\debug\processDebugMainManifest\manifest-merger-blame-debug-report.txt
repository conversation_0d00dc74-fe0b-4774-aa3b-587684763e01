1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.likeqianwang"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="35"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.likeqianwang.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.likeqianwang.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:5:5-39:19
18        android:name="com.example.likeqianwang.LikeQianWangApplication"
18-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:6:9-48
19        android:allowBackup="true"
19-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:7:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2baf259cf119559651ffb6fa809681cc\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:8:9-65
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:fullBackupContent="@xml/backup_rules"
24-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:9:9-54
25        android:icon="@mipmap/ic_launcher"
25-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:10:9-43
26        android:label="@string/app_name"
26-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:11:9-41
27        android:roundIcon="@mipmap/ic_launcher_round"
27-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:12:9-54
28        android:supportsRtl="true"
28-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:13:9-35
29        android:testOnly="true"
30        android:theme="@style/Theme.LikeQianWang" >
30-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:14:9-50
31        <activity
31-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:16:9-18:40
32            android:name="com.example.likeqianwang.FillNewAccountInfoActivity"
32-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:17:13-55
33            android:exported="false" />
33-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:18:13-37
34        <activity
34-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:19:9-21:40
35            android:name="com.example.likeqianwang.TagManagementActivity"
35-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:20:13-50
36            android:exported="false" />
36-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:21:13-37
37        <activity
37-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:22:9-24:40
38            android:name="com.example.likeqianwang.ui.budget_settings.BudgetSettingsActivity"
38-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:23:13-70
39            android:exported="false" />
39-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:24:13-37
40        <activity
40-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:25:9-28:59
41            android:name="com.example.likeqianwang.RecordingPageActivity"
41-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:26:13-50
42            android:exported="false"
42-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:27:13-37
43            android:windowSoftInputMode="adjustNothing" />
43-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:28:13-56
44        <activity
44-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:29:9-38:20
45            android:name="com.example.likeqianwang.MainActivity"
45-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:30:13-41
46            android:exported="true"
46-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:31:13-36
47            android:label="@string/app_name" >
47-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:32:13-45
48            <intent-filter>
48-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:33:13-37:29
49                <action android:name="android.intent.action.MAIN" />
49-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:34:17-69
49-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:34:25-66
50
51                <category android:name="android.intent.category.LAUNCHER" />
51-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:36:17-77
51-->C:\Users\<USER>\Desktop\LikeQianWang\app\src\main\AndroidManifest.xml:36:27-74
52            </intent-filter>
53        </activity>
54
55        <provider
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
56            android:name="androidx.startup.InitializationProvider"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
57            android:authorities="com.example.likeqianwang.androidx-startup"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
58            android:exported="false" >
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
59            <meta-data
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.emoji2.text.EmojiCompatInitializer"
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
61                android:value="androidx.startup" />
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc2a6d649ebc5d43d0c0dc41debff155\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
63-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
64                android:value="androidx.startup" />
64-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\192936837b389adc012ebf64483aee8a\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
67                android:value="androidx.startup" />
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
68        </provider>
69
70        <uses-library
70-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
71            android:name="androidx.window.extensions"
71-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
72            android:required="false" />
72-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
73        <uses-library
73-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
74            android:name="androidx.window.sidecar"
74-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
75            android:required="false" />
75-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a01a7c57b9aa2113291f1ef2ee4b498\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
76
77        <service
77-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
78            android:name="androidx.room.MultiInstanceInvalidationService"
78-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
79            android:directBootAware="true"
79-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
80            android:exported="false" />
80-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7fe7561ca90949058266138f16f9a32\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
81
82        <receiver
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
83            android:name="androidx.profileinstaller.ProfileInstallReceiver"
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
84            android:directBootAware="false"
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
85            android:enabled="true"
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
86            android:exported="true"
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
87            android:permission="android.permission.DUMP" >
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
89                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
92                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
95                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
98                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29d1a091556169af320b473a80886536\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
99            </intent-filter>
100        </receiver>
101    </application>
102
103</manifest>
